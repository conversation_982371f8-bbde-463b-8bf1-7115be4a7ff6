import type { RenderableContinuousEvent, RenderableEvent } from "@/types/haptic-editor";
import { MIN_CURVE_POINT_INTERVAL_MS, MIN_EVENT_INTERVAL_MS, MIN_CONTINUOUS_DURATION } from "../config/waveform-constants";
import { getEventEffectiveDuration } from "../utils/event-space";
import { useDragHelpers } from "./useDragHelpers";

// 边界验证结果类型
export interface BoundaryValidationResult {
  isValid: boolean;
  adjustedValue?: number;
  errorMessage?: string;
  needsTimeExtension?: boolean;
  suggestedTotalDuration?: number;
}

// 曲线点边界计算结果
export interface CurvePointBoundaryResult {
  maxAllowedStartTime?: number;
  minAllowedEndTime?: number;
  isValidForDrag: boolean;
  errorMessage?: string;
}

/**
 * 拖拽边界验证器
 * 专门处理各种复杂的边界检查和验证逻辑
 */
export function useDragBoundaryValidator() {
  const dragHelpers = useDragHelpers();
  const {
    checkTimeBoundary,
    checkEventCollision,
    validateIntensity,
    validateFrequency,
    validateRelativeFrequency,
    validateTimeOffset,
    calculateCurvePointBounds,
    floorTime,
  } = dragHelpers;

  /**
   * 验证事件拖拽的边界条件
   */
  const validateEventDragBoundary = (
    event: RenderableEvent,
    newStartTime: number,
    allEvents: RenderableEvent[],
    totalDuration: number,
    isDurationLockedByAudio: boolean
  ): BoundaryValidationResult => {
    const eventDuration = getEventEffectiveDuration(event);

    // 使用辅助函数进行边界检查
    const boundaryCheck = checkTimeBoundary(
      newStartTime,
      eventDuration,
      totalDuration,
      isDurationLockedByAudio
    );

    // 处理时长自动扩展
    if (boundaryCheck.needsTimeExtension && boundaryCheck.suggestedTotalDuration) {
      return {
        isValid: true,
        adjustedValue: newStartTime,
        needsTimeExtension: true,
        suggestedTotalDuration: boundaryCheck.suggestedTotalDuration
      };
    }

    if (!boundaryCheck.isWithinBounds && boundaryCheck.adjustedValue !== undefined) {
      newStartTime = boundaryCheck.adjustedValue;
    }

    // 使用辅助函数进行碰撞检查
    const collisionCheck = checkEventCollision(event, newStartTime, allEvents);
    if (collisionCheck.hasCollision && collisionCheck.adjustedStartTime !== undefined) {
      newStartTime = collisionCheck.adjustedStartTime;
    }

    return {
      isValid: true,
      adjustedValue: floorTime(newStartTime)
    };
  };

  /**
   * 验证瞬态事件峰值强度的边界
   */
  const validateTransientPeakBoundary = (intensity: number): BoundaryValidationResult => {
    const validation = validateIntensity(intensity);
    return {
      isValid: validation.isValid,
      adjustedValue: validation.adjustedValue,
      errorMessage: validation.errorMessage
    };
  };

  /**
   * 计算第一个曲线点水平拖拽的边界
   * 这是最复杂的边界计算逻辑之一
   */
  const calculateFirstCurvePointBoundary = (
    continuousEvent: RenderableContinuousEvent,
    allEvents: RenderableEvent[]
  ): CurvePointBoundaryResult => {
    const originalEndTime = continuousEvent.stopTime;
    const originalCurves = continuousEvent.curves;
    const originalDuration = continuousEvent.duration;

    // 动态边界检查：计算基于Curve点时间分布的最大允许startTime
    let maxAllowedStartTime = originalEndTime - MIN_CONTINUOUS_DURATION;

    // 如果有第二个Curve点，需要确保第一个点和第二个点之间至少有1ms间隔
    if (originalCurves.length > 1) {
      const secondCurveOriginalTimeOffset = originalCurves[1].timeOffset;

      if (originalDuration > 0 && secondCurveOriginalTimeOffset > 0) {
        // 计算确保第二个点时间偏移至少为最小间隔所需的最小duration
        const minDurationForSecondCurve = originalDuration / secondCurveOriginalTimeOffset * MIN_CURVE_POINT_INTERVAL_MS;
        
        // 基于此计算第一个点的最大允许startTime
        const maxStartTimeBasedOnSecondCurve = originalEndTime - minDurationForSecondCurve;
        maxAllowedStartTime = Math.min(maxAllowedStartTime, maxStartTimeBasedOnSecondCurve);
      } else {
        // 如果原始duration为0或第二个点时间偏移为0，使用保守的边界
        const conservativeMaxStartTime = originalEndTime - (MIN_CURVE_POINT_INTERVAL_MS * 2);
        maxAllowedStartTime = Math.min(maxAllowedStartTime, conservativeMaxStartTime);
      }
    }

    // 检查与前一个事件的碰撞
    const currentEventIndex = allEvents.findIndex(e => e.id === continuousEvent.id);
    if (currentEventIndex > 0) {
      const prevEvent = allEvents[currentEventIndex - 1];
      const minAllowedStartTime = prevEvent.stopTime + MIN_EVENT_INTERVAL_MS;
      if (maxAllowedStartTime < minAllowedStartTime) {
        return {
          isValidForDrag: false,
          errorMessage: "无法拖拽：会与前一个事件发生碰撞"
        };
      }
      maxAllowedStartTime = Math.max(maxAllowedStartTime, minAllowedStartTime);
    }

    return {
      maxAllowedStartTime,
      isValidForDrag: true
    };
  };

  /**
   * 计算最后一个曲线点水平拖拽的边界
   */
  const calculateLastCurvePointBoundary = (
    continuousEvent: RenderableContinuousEvent,
    allEvents: RenderableEvent[]
  ): CurvePointBoundaryResult => {
    const originalStartTime = continuousEvent.startTime;
    const originalCurves = continuousEvent.curves;
    const originalDuration = continuousEvent.duration;

    // 动态边界检查：计算基于Curve点时间分布的最小允许endTime
    let minAllowedEndTime = originalStartTime + MIN_CONTINUOUS_DURATION;

    // 如果有倒数第二个Curve点，需要确保最后一个点和倒数第二个点之间至少有1ms间隔
    if (originalCurves.length > 1) {
      const secondLastCurveIndex = originalCurves.length - 2;
      const secondLastCurveOriginalTimeOffset = originalCurves[secondLastCurveIndex].timeOffset;

      if (originalDuration > 0 && secondLastCurveOriginalTimeOffset < originalDuration) {
        // 计算确保倒数第二个点与最后一个点间隔至少为最小间隔所需的最小duration
        const ratio = secondLastCurveOriginalTimeOffset / originalDuration;
        const minDurationForSecondLastCurve = MIN_CURVE_POINT_INTERVAL_MS / (1 - ratio);
        
        // 基于此计算最后一个点的最小允许endTime
        const minEndTimeBasedOnSecondLastCurve = originalStartTime + minDurationForSecondLastCurve;
        minAllowedEndTime = Math.max(minAllowedEndTime, minEndTimeBasedOnSecondLastCurve);
      } else {
        // 如果原始duration为0或倒数第二个点时间偏移异常，使用保守的边界
        const conservativeMinEndTime = originalStartTime + (MIN_CURVE_POINT_INTERVAL_MS * 2);
        minAllowedEndTime = Math.max(minAllowedEndTime, conservativeMinEndTime);
      }
    }

    // 检查与下一个事件的碰撞
    const currentEventIndex = allEvents.findIndex(e => e.id === continuousEvent.id);
    if (currentEventIndex < allEvents.length - 1) {
      const nextEvent = allEvents[currentEventIndex + 1];
      const maxAllowedEndTime = nextEvent.startTime - MIN_EVENT_INTERVAL_MS;
      if (minAllowedEndTime > maxAllowedEndTime) {
        return {
          isValidForDrag: false,
          errorMessage: "无法拖拽：会与下一个事件发生碰撞"
        };
      }
    }

    return {
      minAllowedEndTime,
      isValidForDrag: true
    };
  };

  /**
   * 验证第一个曲线点的水平拖拽
   */
  const validateFirstCurvePointHorizontalDrag = (
    continuousEvent: RenderableContinuousEvent,
    newStartTime: number,
    allEvents: RenderableEvent[]
  ): BoundaryValidationResult => {
    const boundary = calculateFirstCurvePointBoundary(continuousEvent, allEvents);
    
    if (!boundary.isValidForDrag) {
      return {
        isValid: false,
        errorMessage: boundary.errorMessage
      };
    }

    const originalEndTime = continuousEvent.stopTime;
    let adjustedStartTime = newStartTime;
    let newDuration = originalEndTime - adjustedStartTime;

    // 验证边界条件
    if (adjustedStartTime < 0) {
      adjustedStartTime = 0;
      newDuration = originalEndTime - adjustedStartTime;
    }

    if (boundary.maxAllowedStartTime !== undefined && adjustedStartTime > boundary.maxAllowedStartTime) {
      adjustedStartTime = boundary.maxAllowedStartTime;
      newDuration = originalEndTime - adjustedStartTime;
    }

    // 最终验证：确保duration不小于最小值
    if (newDuration < MIN_CONTINUOUS_DURATION) {
      return {
        isValid: false,
        errorMessage: `调整后的持续时间过短，最小值为 ${MIN_CONTINUOUS_DURATION}ms`
      };
    }

    return {
      isValid: true,
      adjustedValue: floorTime(adjustedStartTime)
    };
  };

  /**
   * 验证最后一个曲线点的水平拖拽
   */
  const validateLastCurvePointHorizontalDrag = (
    continuousEvent: RenderableContinuousEvent,
    newEndTime: number,
    allEvents: RenderableEvent[]
  ): BoundaryValidationResult => {
    const boundary = calculateLastCurvePointBoundary(continuousEvent, allEvents);
    
    if (!boundary.isValidForDrag) {
      return {
        isValid: false,
        errorMessage: boundary.errorMessage
      };
    }

    const originalStartTime = continuousEvent.startTime;
    let adjustedEndTime = newEndTime;
    let newDuration = adjustedEndTime - originalStartTime;

    // 验证边界条件
    if (boundary.minAllowedEndTime !== undefined && adjustedEndTime < boundary.minAllowedEndTime) {
      adjustedEndTime = boundary.minAllowedEndTime;
      newDuration = adjustedEndTime - originalStartTime;
    }

    // 检查与下一个事件的碰撞
    const currentEventIndex = allEvents.findIndex(e => e.id === continuousEvent.id);
    if (currentEventIndex < allEvents.length - 1) {
      const nextEvent = allEvents[currentEventIndex + 1];
      const maxAllowedEndTime = nextEvent.startTime - MIN_EVENT_INTERVAL_MS;
      if (adjustedEndTime > maxAllowedEndTime) {
        adjustedEndTime = maxAllowedEndTime;
        newDuration = adjustedEndTime - originalStartTime;
      }
    }

    // 最终验证：确保duration不小于最小值
    if (newDuration < MIN_CONTINUOUS_DURATION) {
      return {
        isValid: false,
        errorMessage: `调整后的持续时间过短，最小值为 ${MIN_CONTINUOUS_DURATION}ms`
      };
    }

    return {
      isValid: true,
      adjustedValue: floorTime(adjustedEndTime)
    };
  };

  /**
   * 验证曲线点频率调整的边界
   */
  const validateCurvePointFrequencyBoundary = (
    newRelativeFrequency: number,
    eventFrequency: number,
    minFreq: number,
    maxFreq: number
  ): BoundaryValidationResult => {
    // 验证相对频率
    const relativeFreqValidation = validateRelativeFrequency(newRelativeFrequency);
    if (!relativeFreqValidation.isValid) {
      return {
        isValid: false,
        adjustedValue: relativeFreqValidation.adjustedValue,
        errorMessage: relativeFreqValidation.errorMessage
      };
    }

    // 验证绝对频率
    const newAbsoluteFrequency = newRelativeFrequency + eventFrequency;
    const absoluteFreqValidation = validateFrequency(newAbsoluteFrequency, minFreq, maxFreq);
    
    return {
      isValid: absoluteFreqValidation.isValid,
      adjustedValue: absoluteFreqValidation.adjustedValue,
      errorMessage: absoluteFreqValidation.errorMessage
    };
  };

  /**
   * 验证曲线点位置和强度调整的边界
   */
  const validateCurvePointPositionBoundary = (
    continuousEvent: RenderableContinuousEvent,
    curveIndex: number,
    newTimeOffset: number,
    newIntensity: number
  ): BoundaryValidationResult => {
    // 验证时间偏移
    const bounds = calculateCurvePointBounds(continuousEvent, curveIndex);
    const timeOffsetValidation = validateTimeOffset(newTimeOffset, bounds.minTimeOffset, bounds.maxTimeOffset);
    
    if (!timeOffsetValidation.isValid) {
      return {
        isValid: false,
        adjustedValue: timeOffsetValidation.adjustedValue,
        errorMessage: timeOffsetValidation.errorMessage
      };
    }

    // 验证强度（限制在事件强度范围内）
    const intensityValidation = validateIntensity(newIntensity, continuousEvent.eventIntensity);
    
    if (!intensityValidation.isValid) {
      return {
        isValid: false,
        adjustedValue: intensityValidation.adjustedValue,
        errorMessage: intensityValidation.errorMessage
      };
    }

    return {
      isValid: true,
      adjustedValue: timeOffsetValidation.adjustedValue
    };
  };

  return {
    // 事件拖拽边界验证
    validateEventDragBoundary,
    
    // 瞬态事件边界验证
    validateTransientPeakBoundary,
    
    // 曲线点边界计算
    calculateFirstCurvePointBoundary,
    calculateLastCurvePointBoundary,
    
    // 曲线点拖拽边界验证
    validateFirstCurvePointHorizontalDrag,
    validateLastCurvePointHorizontalDrag,
    validateCurvePointFrequencyBoundary,
    validateCurvePointPositionBoundary,
    
    // 重新导出基础验证函数
    validateIntensity,
    validateFrequency,
    validateRelativeFrequency,
    validateTimeOffset,
    floorTime,
  };
}
