import { defineStore } from "pinia";
import type { FileEditorState, RenderableEvent, RenderableContinuousEvent } from "./types";
import { validateFileUuid } from "./utils/validation";
import { fileEditorStateManager } from "./state-manager";
import { convertToSelectedEvent } from "./utils/converters";
import { calculateEventsTotalDuration } from "./utils/converters";

// 导入所有actions
import { createSelectionActions, type SelectionActions } from "./actions/selection-actions";
import { createDurationActions, type DurationActions } from "./actions/duration-actions";
import { createEventActions, type EventActions } from "./actions/event-actions";
import { createBatchActions, type BatchActions } from "./actions/batch-actions";
import { createHistoryActions, type HistoryActions } from "./actions/history-actions";
import { createAudioActions, type AudioActions } from "./actions/audio-actions";



import { logger, LogModule } from "@/utils/logger/logger";
import { checkForRealEventDataChanges } from "@/components/editor/waveform/utils/event-change-detector";

// 音频振幅数据接口
export interface AudioAmplitudeData {
  samples: number[];
  sample_rate: number;
  duration_ms: number;
  max_amplitude: number;
  min_amplitude: number;
}



/**
 * 所有Actions的组合接口
 */
export interface AllActions extends SelectionActions, DurationActions, EventActions, BatchActions, HistoryActions, AudioActions {
  // 可以在这里添加其他通用的actions
}

/**
 * Store的完整接口
 */
export interface HapticsEditorStore {
  // State
  selectedEventId: string | null;
  selectedCurvePointIndex: number;
  events: RenderableEvent[];
  totalDuration: number;
  isDurationLockedByAudio: boolean;
  isAdjustingProperties: boolean;

  // Getters (内联定义)
  getters: {
    selectedEvent: () => any;
    selectedRenderableEvent: () => RenderableEvent | null;
    selectedCurvePoint: () => any;
    hasSelection: () => boolean;
    hasCurvePointSelection: () => boolean;
    currentTotalDuration: () => number;
    isTimelineDurationAdjustable: () => boolean;
    isDurationLocked: () => boolean;
    getEffectiveDuration: () => number;
    eventsCount: () => number;
    hasEvents: () => boolean;
    getEventById: (eventId: string) => RenderableEvent | null;
  };

  // Actions
  actions: AllActions;
}

/**
 * 创建文件级别的 Waveform Editor Store
 */
export function createFileWaveformEditorStore(fileUuid: string) {
  // 验证fileUuid
  const validatedFileUuid = validateFileUuid(fileUuid);

  return defineStore(`waveformEditor-${validatedFileUuid}`, {
    state: (): FileEditorState & { _actionsInitialized?: boolean; _actions?: any } => {
      // 从状态管理器获取或创建文件状态
      const fileState = fileEditorStateManager.getFileState(validatedFileUuid);

      logger.debug(LogModule.WAVEFORM, `初始化文件级别Store: ${validatedFileUuid}`, {
        eventsCount: fileState.events.length,
        totalDuration: fileState.totalDuration,
      });

      return {
        ...fileState,
        _actionsInitialized: false,
        _actions: null,
      };
    },

    getters: {
      // 选择相关的getters
      selectedEvent(state) {
        const event = state.events.find((e) => e.id === state.selectedEventId);
        return convertToSelectedEvent(event || null);
      },

      selectedRenderableEvent(state) {
        return state.events.find((e) => e.id === state.selectedEventId) || null;
      },

      selectedCurvePoint(state) {
        const event = state.events.find((e) => e.id === state.selectedEventId);
        if (
          !event ||
          event.type !== "continuous" ||
          state.selectedCurvePointIndex < 0 ||
          state.selectedCurvePointIndex >= (event as RenderableContinuousEvent).curves.length
        ) {
          return null;
        }
        return (event as RenderableContinuousEvent).curves[state.selectedCurvePointIndex];
      },

      hasSelection(state) {
        return state.selectedEventId !== null;
      },

      hasCurvePointSelection(state) {
        return state.selectedCurvePointIndex >= 0;
      },

      // 时长相关的getters
      currentTotalDuration(state) {
        return state.totalDuration;
      },

      isTimelineDurationAdjustable(state) {
        return !state.isDurationLockedByAudio;
      },

      isDurationLocked(state) {
        return state.isDurationLockedByAudio;
      },

      getEffectiveDuration(state) {
        const eventsDuration = calculateEventsTotalDuration(state.events);
        return Math.max(state.totalDuration, eventsDuration);
      },

      // 通用getters
      eventsCount(state) {
        return state.events.length;
      },

      hasEvents(state) {
        return state.events.length > 0;
      },

      getEventById: (state) => (eventId: string) => {
        return state.events.find((event) => event.id === eventId) || null;
      }
    },

    actions: {
      // 内部状态更新方法
      _setState(updates: Partial<FileEditorState>) {
        Object.assign(this, updates);

        // 同步到文件状态管理器
        fileEditorStateManager.setFileState(validatedFileUuid, {
          events: [...this.events],
          totalDuration: this.totalDuration,
          selectedEventId: this.selectedEventId,
          selectedCurvePointIndex: this.selectedCurvePointIndex,
          isDurationLockedByAudio: this.isDurationLockedByAudio,
          isAdjustingProperties: this.isAdjustingProperties,
          audioAmplitudeData: this.audioAmplitudeData,
          audioDuration: this.audioDuration,
        });
      },

      // 文件状态变化回调
      _onFileStateChange(fileUuid: string, events: RenderableEvent[]) {
        fileEditorStateManager.setFileState(fileUuid, {
          events: [...events],
          totalDuration: this.totalDuration,
          selectedEventId: this.selectedEventId,
          selectedCurvePointIndex: this.selectedCurvePointIndex,
          audioAmplitudeData: this.audioAmplitudeData,
          audioDuration: this.audioDuration,
        });
      },

      // 延迟初始化actions的方法
      _initializeActions() {
        if (this._actionsInitialized) return this._actions;

        const state = this;
        const setState = (updates: Partial<FileEditorState>, options?: {
          skipHistoryRecord?: boolean;
          source?: string;
          isFileInitialLoad?: boolean;
        }) => {
          // 保存更新前的状态，用于检测变化
          const previousEvents = [...state.events];
          const hasEventsUpdate = 'events' in updates;
          const skipHistoryRecord = options?.skipHistoryRecord || false;
          const source = options?.source || 'user_action';
          const isFileInitialLoad = options?.isFileInitialLoad || false;

          Object.assign(state, updates);

          // 同步到文件状态管理器
          fileEditorStateManager.setFileState(validatedFileUuid, {
            events: [...state.events],
            totalDuration: state.totalDuration,
            selectedEventId: state.selectedEventId,
            selectedCurvePointIndex: state.selectedCurvePointIndex,
            isDurationLockedByAudio: state.isDurationLockedByAudio,
            isAdjustingProperties: state.isAdjustingProperties,
            audioAmplitudeData: state.audioAmplitudeData,
            audioDuration: state.audioDuration,
          });

          // 🎯 统一的历史记录触发机制
          // 只有当events数组发生实际变化时才触发历史记录
          if (hasEventsUpdate && !skipHistoryRecord) {
            logger.debug(LogModule.HISTORY, "🎯 检测到events更新", {
              fileUuid: validatedFileUuid,
              previousCount: previousEvents.length,
              currentCount: state.events.length,
              updateKeys: Object.keys(updates),
              skipHistoryRecord
            });

            // 检查是否为实质性的数据变化
            const hasRealDataChange = checkForRealEventDataChanges(state.events, previousEvents);

            logger.debug(LogModule.HISTORY, "🎯 数据变化检测结果", {
              fileUuid: validatedFileUuid,
              hasRealDataChange,
              previousCount: previousEvents.length,
              currentCount: state.events.length,
              skipHistoryRecord
            });

            if (hasRealDataChange) {
              // 使用防抖机制避免频繁的历史记录添加
              import("@/utils/performance/UnifiedDebounceManager").then(({ debounce }) => {
                debounce(
                  `auto-history-${validatedFileUuid}`,
                  () => {
                    // 延迟执行，确保当前的setState调用完成
                    setTimeout(() => {
                      try {
                        // 直接调用store的addHistoryRecord方法，避免在setState内部调用_initializeActions
                        if (state.addHistoryRecord) {
                          const success = state.addHistoryRecord("数据变化", { source, isFileInitialLoad });
                          if (success) {
                            logger.debug(LogModule.HISTORY, "🎯 统一历史记录已自动添加", {
                              fileUuid: validatedFileUuid,
                              eventsCount: state.events.length,
                              source,
                              trigger: "setState"
                            });
                          } else {
                            // 根据数据来源调整日志级别和信息
                            const isFileLoadScenario = isFileInitialLoad || source === 'file_load' || source === 'cache_restore';
                            if (isFileLoadScenario) {
                              logger.debug(LogModule.HISTORY, "🎯 文件加载场景：智能跳过重复历史记录", {
                                fileUuid: validatedFileUuid,
                                eventsCount: state.events.length,
                                source,
                                reason: "系统检测到重复数据，这是正常的保护机制"
                              });
                            } else {
                              logger.warn(LogModule.HISTORY, "🎯 统一历史记录添加失败", {
                                fileUuid: validatedFileUuid,
                                eventsCount: state.events.length,
                                source,
                                reason: "可能原因：数据无显著变化或历史管理器未正确初始化"
                              });
                            }
                          }
                        } else {
                          logger.warn(LogModule.HISTORY, "🎯 addHistoryRecord方法不可用", {
                            fileUuid: validatedFileUuid
                          });
                        }
                      } catch (error) {
                        logger.warn(LogModule.HISTORY, "自动添加历史记录失败", error);
                      }
                    }, 100); // 增加延迟时间，确保初始化完成
                  },
                  200, // 200ms防抖，避免快速连续操作产生过多历史记录
                  'low' // 自动历史记录使用低优先级
                );
              });
            } else {
              logger.debug(LogModule.HISTORY, "🎯 events数组无实质性变化，跳过历史记录", {
                fileUuid: validatedFileUuid,
                previousCount: previousEvents.length,
                currentCount: state.events.length
              });
            }
          }
        };

        const onFileStateChange = (fileUuid: string, events: RenderableEvent[]) => {
          fileEditorStateManager.setFileState(fileUuid, {
            events: [...events],
            totalDuration: state.totalDuration,
            selectedEventId: state.selectedEventId,
            selectedCurvePointIndex: state.selectedCurvePointIndex,
          });
        };

        // 创建各个模块的actions
        const selectionActions = createSelectionActions(state, setState);
        const durationActions = createDurationActions(state, setState);
        const eventActions = createEventActions(state, setState, validatedFileUuid, onFileStateChange);
        const audioActions = createAudioActions(state, setState);

        // 先创建一个临时的setBatchEvents函数引用
        let setBatchEventsRef: ((events: RenderableEvent[], options?: any) => void) | null = null;

        const historyActions = createHistoryActions(state, (events, options) => {
          if (setBatchEventsRef) {
            setBatchEventsRef(events, options);
          }
        });

        const batchActions = createBatchActions(
          state,
          setState,
          validatedFileUuid,
          onFileStateChange,
          historyActions.ensureHistoryInitialized
        );

        // 设置真正的setBatchEvents函数引用
        setBatchEventsRef = batchActions.setBatchEvents;

        // 缓存actions
        this._actions = {
          ...selectionActions,
          ...durationActions,
          ...eventActions,
          ...batchActions,
          ...historyActions,
          ...audioActions
        };

        this._actionsInitialized = true;
        return this._actions;
      },

      // 代理所有action方法
      selectEvent(eventId: string | null) {
        const actions = this._initializeActions();
        return actions.selectEvent(eventId);
      },

      selectCurvePoint(index: number) {
        const actions = this._initializeActions();
        return actions.selectCurvePoint(index);
      },

      setAdjustingProperties(isAdjusting: boolean) {
        const actions = this._initializeActions();
        return actions.setAdjustingProperties(isAdjusting);
      },

      clearSelection() {
        const actions = this._initializeActions();
        return actions.clearSelection();
      },

      updateSelectedEvent(eventDetail: any, options?: { preserveOtherCurveIntensities?: boolean }) {
        const actions = this._initializeActions();
        return actions.updateSelectedEvent(eventDetail, options);
      },

      updateBaseDurations(effectDurationMs: number, audioDurationMs: number | null) {
        const actions = this._initializeActions();
        return actions.updateBaseDurations(effectDurationMs, audioDurationMs);
      },

      increaseTotalDurationByUser(durationToAddMs: number) {
        const actions = this._initializeActions();
        return actions.increaseTotalDurationByUser(durationToAddMs);
      },

      recalculateTotalDuration() {
        const actions = this._initializeActions();
        return actions.recalculateTotalDuration();
      },

      setTotalDuration(duration: number) {
        const actions = this._initializeActions();
        return actions.setTotalDuration(duration);
      },

      lockDurationByAudio(audioDuration: number) {
        const actions = this._initializeActions();
        return actions.lockDurationByAudio(audioDuration);
      },

      unlockDuration() {
        const actions = this._initializeActions();
        return actions.unlockDuration();
      },

      createNewEvent(eventConfig: any) {
        const actions = this._initializeActions();
        return actions.createNewEvent(eventConfig);
      },

      deleteSelectedEvent() {
        const actions = this._initializeActions();
        return actions.deleteSelectedEvent();
      },

      deleteEventById(eventId: string) {
        const actions = this._initializeActions();
        return actions.deleteEventById(eventId);
      },

      updateEvent(eventId: string, updates: any) {
        const actions = this._initializeActions();
        return actions.updateEvent(eventId, updates);
      },

      setEvents(events: RenderableEvent[], options?: {
        source?: string;
        isFileInitialLoad?: boolean;
        skipHistoryRecord?: boolean;
      }) {
        const actions = this._initializeActions();
        return actions.setEvents(events, options);
      },

      setBatchEvents(events: RenderableEvent[], options?: any) {
        const actions = this._initializeActions();
        return actions.setBatchEvents(events, options);
      },

      updateBatchEvents(updates: any[], options?: any) {
        const actions = this._initializeActions();
        return actions.updateBatchEvents(updates, options);
      },

      replaceEventsInRange(startTime: number, endTime: number, newEvents: RenderableEvent[], options?: any) {
        const actions = this._initializeActions();
        return actions.replaceEventsInRange(startTime, endTime, newEvents, options);
      },

      initHistorySystem() {
        const actions = this._initializeActions();
        return actions.initHistorySystem();
      },

      ensureHistoryInitialized() {
        const actions = this._initializeActions();
        return actions.ensureHistoryInitialized();
      },

      getHistorySystem() {
        const actions = this._initializeActions();
        return actions.getHistorySystem();
      },

      undo() {
        const actions = this._initializeActions();
        return actions.undo();
      },

      redo() {
        const actions = this._initializeActions();
        return actions.redo();
      },

      canUndo() {
        const actions = this._initializeActions();
        return actions.canUndo();
      },

      canRedo() {
        const actions = this._initializeActions();
        return actions.canRedo();
      },

      addHistoryRecord(description?: string) {
        const actions = this._initializeActions();
        return actions.addHistoryRecord(description);
      },

      getHistoryState() {
        const actions = this._initializeActions();
        return actions.getHistoryState();
      },

      configureHistory(options: any) {
        const actions = this._initializeActions();
        return actions.configureHistory(options);
      },

      clearHistory() {
        const actions = this._initializeActions();
        return actions.clearHistory();
      },

      destroyHistorySystem() {
        const actions = this._initializeActions();
        return actions.destroyHistorySystem();
      },

      // 音频相关方法
      loadAudioData(projectDirPath: string, audioRelativePath: string, maxSamples?: number) {
        const actions = this._initializeActions();
        return actions.loadAudioData(projectDirPath, audioRelativePath, maxSamples);
      },

      setAudioData(data: AudioAmplitudeData | null) {
        const actions = this._initializeActions();
        return actions.setAudioData(data);
      },

      clearAudioData() {
        const actions = this._initializeActions();
        return actions.clearAudioData();
      },

      getAudioData() {
        const actions = this._initializeActions();
        return actions.getAudioData();
      },

      hasAudioData() {
        const actions = this._initializeActions();
        return actions.hasAudioData();
      },

      retryAudioLoad(projectDirPath: string, audioRelativePath: string, maxSamples?: number) {
        const actions = this._initializeActions();
        return actions.retryAudioLoad(projectDirPath, audioRelativePath, maxSamples);
      },

      // 音频波形显示状态管理已移除：现在根据音频数据自动显示

      // 数据验证方法
      validateEventsData(events: RenderableEvent[]) {
        const actions = this._initializeActions();
        // 使用batch actions中的验证逻辑
        try {
          actions.setBatchEvents(events, { skipValidation: false, skipFileStateSync: true });
          return { valid: true, errors: [] };
        } catch (error) {
          return {
            valid: false,
            errors: [error instanceof Error ? error.message : String(error)]
          };
        }
      }
    },
  });
}

// 文件级别的 Store 实例缓存
const fileStoreInstances = new Map<string, ReturnType<typeof createFileWaveformEditorStore>>();

/**
 * 获取或创建文件级别的 Store 实例
 */
export function useFileWaveformEditorStore(fileUuid: string) {
  const validatedFileUuid = validateFileUuid(fileUuid);

  if (!fileStoreInstances.has(validatedFileUuid)) {
    const storeFactory = createFileWaveformEditorStore(validatedFileUuid);
    fileStoreInstances.set(validatedFileUuid, storeFactory);
  }
  return fileStoreInstances.get(validatedFileUuid)!();
}

/**
 * 清理文件 Store 实例
 */
export function clearFileStoreInstance(fileUuid: string) {
  const validatedFileUuid = validateFileUuid(fileUuid);
  fileStoreInstances.delete(validatedFileUuid);
  fileEditorStateManager.removeFileState(validatedFileUuid);
}

/**
 * 清理所有文件 Store 实例
 */
export function clearAllFileStoreInstances() {
  fileStoreInstances.clear();
  fileEditorStateManager.clearAllStates();
}

// 导出类型和工具
export * from "./types";
export * from "./utils/validation";
export { fileEditorStateManager } from "./state-manager";
