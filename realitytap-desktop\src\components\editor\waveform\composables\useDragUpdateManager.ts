import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import type { DragTarget } from "./useDragState";
import type { DragHandlerConfig } from "./useDragConfig";
import { calculateRawIntensity } from "../utils/coordinate";
import { MIN_CONTINUOUS_DURATION } from "../config/waveform-constants";
import { useDragBoundaryValidator } from "./useDragBoundaryValidator";
import { dragLogger } from "@/utils/logger/logger";
import type { Ref } from "vue";

/**
 * 拖拽更新管理器
 * 专门处理拖拽完成后的数据更新逻辑
 */
export function useDragUpdateManager(
  config: DragHandlerConfig,
  // 拖拽状态
  draggedEvent: Ref<RenderableEvent | null>,
  draggingTarget: Ref<DragTarget | null>,
  draggedCurveIndex: Ref<number>,
  dragStartEventTime: Ref<number>,
  dragStartX: Ref<number>,
  currentDraggedIntensity: Ref<number>,
  currentDraggedTimeOffset: Ref<number>,
  currentDraggedRelativeFrequency: Ref<number>,
  // 辅助函数
  isFrequencyAdjustmentKeyPressed: () => boolean,
  getEvents: () => RenderableEvent[]
) {
  const boundaryValidator = useDragBoundaryValidator();
  const { validateEventDragBoundary, validateTransientPeakBoundary, validateCurvePointPositionBoundary, validateRelativeFrequency, floorTime } = boundaryValidator;

  /**
   * 生成拖拽操作描述（用于历史记录）
   */
  const getDragDescription = (targetType: DragTarget | null, event: RenderableEvent | null): string => {
    if (!targetType || !event) {
      return "拖拽操作";
    }

    const eventType = event.type === "transient" ? "瞬态事件" : "连续事件";

    switch (targetType) {
      case "event":
        return `拖拽${eventType}位置`;
      case "transientPeak":
        return "调整瞬态事件强度";
      case "continuousCurvePoint":
        return "调整连续事件曲线点";
      default:
        return "拖拽操作";
    }
  };

  /**
   * 更新事件位置
   */
  const updateEventPosition = (mouseEvent: MouseEvent, event: RenderableEvent) => {
    const canvas = config.canvas.value;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const finalX = (mouseEvent.clientX - rect.left) * dpr;
    const deltaX = finalX - dragStartX.value;
    const timeOffset = config.mapXOffsetToTimeOffsetLocal(deltaX);
    let finalNewStartTime = dragStartEventTime.value + timeOffset;

    // 使用边界验证器进行验证
    const validation = validateEventDragBoundary(event, finalNewStartTime, getEvents(), config.waveformStore.totalDuration, config.waveformStore.isDurationLockedByAudio);

    // 处理时长自动扩展
    if (validation.needsTimeExtension && validation.suggestedTotalDuration) {
      config.waveformStore.totalDuration = validation.suggestedTotalDuration;
    }

    // 应用验证结果，并添加额外的安全检查确保与实时更新逻辑一致
    // 这里使用 Math.max(0, ...) 确保最终时间不会是负数，与拖拽过程中的约束逻辑保持一致
    // 修复问题：当用户拖拽到时间轴左边界外时，确保最终位置与拖拽过程中的显示一致
    finalNewStartTime = Math.max(0, validation.adjustedValue || finalNewStartTime);

    // 创建更新载荷
    const updatePayload: any = {
      Type: event.type,
      RelativeTime: finalNewStartTime,
      Parameters: {
        Intensity: event.type === "transient" ? (event as RenderableTransientEvent).intensity : (event as RenderableContinuousEvent).eventIntensity,
        Frequency: event.type === "transient" ? (event as RenderableTransientEvent).frequency : (event as RenderableContinuousEvent).eventFrequency,
      },
    };

    if (event.type === "continuous") {
      updatePayload.Duration = (event as RenderableContinuousEvent).duration;
    }

    // 更新store
    config.waveformStore.updateSelectedEvent(updatePayload);
  };

  /**
   * 更新瞬态事件峰值强度
   */
  const updateTransientPeakIntensity = (event: RenderableEvent) => {
    const finalIntensity = currentDraggedIntensity.value;

    // 使用边界验证器验证强度值
    const validation = validateTransientPeakBoundary(finalIntensity);
    const validatedIntensity = validation.adjustedValue || finalIntensity;

    const updatePayload: any = {
      Type: "transient",
      RelativeTime: (event as RenderableTransientEvent).startTime,
      Parameters: {
        Intensity: validatedIntensity,
        Frequency: (event as RenderableTransientEvent).frequency,
      },
    };

    // 更新store
    config.waveformStore.updateSelectedEvent(updatePayload);
  };

  /**
   * 更新连续事件曲线点
   */
  const updateCurvePoint = (event: RenderableEvent) => {
    if (draggedCurveIndex.value < 0) return;

    const continuousEvent = event as RenderableContinuousEvent;
    const curves = continuousEvent.curves;

    // 特殊处理：第一个Curve点的水平拖拽（调整事件startTime和duration）
    if (draggedCurveIndex.value === 0 && !isFrequencyAdjustmentKeyPressed()) {
      return updateFirstCurvePointHorizontalDrag(continuousEvent);
    }

    // 特殊处理：最后一个Curve点的水平拖拽（调整事件endTime和duration）
    if (draggedCurveIndex.value === curves.length - 1 && !isFrequencyAdjustmentKeyPressed()) {
      return updateLastCurvePointHorizontalDrag(continuousEvent);
    }

    // 其他曲线点的常规处理
    return updateRegularCurvePoint(continuousEvent);
  };

  /**
   * 更新第一个曲线点的水平拖拽
   */
  const updateFirstCurvePointHorizontalDrag = (continuousEvent: RenderableContinuousEvent) => {
    const newStartTime = continuousEvent.startTime + currentDraggedTimeOffset.value;
    const originalEndTime = continuousEvent.stopTime;
    const newDuration = originalEndTime - newStartTime;

    const updatePayload: any = {
      Type: "continuous",
      RelativeTime: Math.max(0, floorTime(newStartTime)),
      Duration: Math.max(MIN_CONTINUOUS_DURATION, Math.floor(newDuration)),
      Parameters: {
        Intensity: continuousEvent.eventIntensity,
        Frequency: continuousEvent.eventFrequency,
      },
    };

    config.waveformStore.updateSelectedEvent(updatePayload);
  };

  /**
   * 更新最后一个曲线点的水平拖拽
   */
  const updateLastCurvePointHorizontalDrag = (continuousEvent: RenderableContinuousEvent) => {
    const originalStartTime = continuousEvent.startTime;
    const newDuration = continuousEvent.duration + currentDraggedTimeOffset.value;

    const updatePayload: any = {
      Type: "continuous",
      RelativeTime: originalStartTime,
      Duration: Math.max(MIN_CONTINUOUS_DURATION, Math.floor(newDuration)),
      Parameters: {
        Intensity: continuousEvent.eventIntensity,
        Frequency: continuousEvent.eventFrequency,
      },
    };

    config.waveformStore.updateSelectedEvent(updatePayload);
  };

  /**
   * 更新常规曲线点
   */
  const updateRegularCurvePoint = (continuousEvent: RenderableContinuousEvent) => {
    const updatePayload: any = {
      Type: "continuous",
      updateType: "UPDATE_CURVE_POINT",
      curveIndex: draggedCurveIndex.value,
    };

    if (isFrequencyAdjustmentKeyPressed()) {
      // 更新相对频率
      const relativeFreq = currentDraggedRelativeFrequency.value;
      const validation = validateRelativeFrequency(relativeFreq);
      updatePayload.relativeCurveFrequency = validation.adjustedValue || relativeFreq;
    } else {
      // 更新位置和强度
      if (draggedCurveIndex.value > 0 && draggedCurveIndex.value < continuousEvent.curves.length - 1) {
        // 验证位置和强度
        const validation = validateCurvePointPositionBoundary(continuousEvent, draggedCurveIndex.value, currentDraggedTimeOffset.value, currentDraggedIntensity.value);

        if (validation.isValid) {
          updatePayload.newTimeOffset = validation.adjustedValue || currentDraggedTimeOffset.value;
          updatePayload.rawIntensity = calculateRawIntensity(currentDraggedIntensity.value, continuousEvent.eventIntensity);
        }
      }
    }

    // 只有当有实际需要更新的参数时才调用
    if (Object.keys(updatePayload).length > 3) {
      config.waveformStore.updateSelectedEvent(updatePayload);
    }
  };

  /**
   * 添加拖拽历史记录 - 已由统一机制处理，保留函数以兼容现有调用
   */
  const addDragHistoryRecord = (_targetType: DragTarget | null, _event: RenderableEvent | null) => {
    // 历史记录现在由store的统一机制自动处理，无需手动添加
    // 保留此函数以避免破坏现有的调用代码
  };

  /**
   * 执行拖拽更新操作
   */
  const executeDragUpdate = (mouseEvent: MouseEvent): boolean => {
    const currentDraggedEvent = draggedEvent.value;
    const currentTarget = draggingTarget.value;

    if (!currentDraggedEvent || !currentTarget) {
      return false;
    }

    try {
      // 根据拖动目标类型执行不同的更新逻辑
      if (currentTarget === "event") {
        updateEventPosition(mouseEvent, currentDraggedEvent);
      } else if (currentTarget === "transientPeak" && currentDraggedEvent.type === "transient") {
        updateTransientPeakIntensity(currentDraggedEvent);
      } else if (currentTarget === "continuousCurvePoint" && currentDraggedEvent.type === "continuous") {
        updateCurvePoint(currentDraggedEvent);
      }

      // 添加历史记录
      addDragHistoryRecord(currentTarget, currentDraggedEvent);

      return true;
    } catch (error) {
      dragLogger.error("拖拽更新失败", error);
      return false;
    }
  };

  return {
    // 主要更新函数
    updateEventPosition,
    updateTransientPeakIntensity,
    updateCurvePoint,

    // 执行更新
    executeDragUpdate,

    // 历史记录
    addDragHistoryRecord,
    getDragDescription,
  };
}
