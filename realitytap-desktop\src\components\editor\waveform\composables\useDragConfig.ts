import type { RenderableEvent } from "@/types/haptic-editor";
import type { Ref } from "vue";

// 拖拽处理器配置接口
export interface DragHandlerConfig {
  canvas: Ref<HTMLCanvasElement | null>;
  events: RenderableEvent[] | (() => RenderableEvent[]); // 支持静态数组或动态函数
  waveformStore: {
    selectedEventId: string | null;
    selectedCurvePointIndex: number;
    totalDuration: number;
    isDurationLockedByAudio: boolean;
    selectEvent: (eventId: string | null) => void;
    selectCurvePoint: (index: number) => void;
    updateSelectedEvent: (updatedEvent: any, options?: { preserveOtherCurveIntensities?: boolean }) => void;
    // addHistoryRecord 已移除 - 现在由统一机制自动处理
  };
  mapTimeToXLocal: (time: number) => number;
  mapIntensityToYLocal: (intensity: number, maxIntensity?: number) => number;
  mapYToIntensityLocal: (y: number, maxIntensity?: number) => number;
  mapXOffsetToTimeOffsetLocal: (xOffset: number) => number;
  mapXToTimeOffsetLocal: (x: number) => number;
  smartDrawWaveform: (forceRedraw?: boolean) => void;
  invalidateEventCache: (eventId: string) => void;
  isFrequencyAdjustmentKeyPressed: Ref<boolean>;
  DRAG_THRESHOLD_MS: number;
  audioDuration?: number | null;
  setDragEndTime: () => void;
  // 事件tooltip相关回调
  showEventTooltip?: (x: number, y: number) => void;
  hideEventTooltip?: () => void;
  updateEventTooltipPosition?: (x: number, y: number) => void;
}

// 自动滚动配置接口
export interface AutoScrollConfig {
  horizontalScrollbarRef: Ref<any>;
  PADDING: { left: number; right: number };
  logicalCanvasWidth: Ref<number>;
  virtualScrollOffset: Ref<number>;
  scrollLeftValue: Ref<number>;
  getEffectiveDuration: () => number;
  getLogicalGraphAreaWidth: () => number;
  // 容器相关配置
  containerRef?: Ref<HTMLElement | null>;
  graphContainerRef?: Ref<HTMLElement | null>;
  // 坐标映射函数
  mapTimeToXLocal?: (time: number) => number;
}

/**
 * 拖拽配置管理 composable
 * 提供拖拽相关的配置接口和类型定义
 */
export function useDragConfig() {
  // 这个 composable 主要用于类型导出，不包含具体逻辑
  return {};
}
