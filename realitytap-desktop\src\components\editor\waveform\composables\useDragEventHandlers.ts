import type { RenderableContinuousEvent, RenderableEvent } from "@/types/haptic-editor";
import type { DragTarget } from "./useDragState";
import type { DragHandlerConfig, AutoScrollConfig } from "./useDragConfig";
import { dragLogger } from "@/utils/logger/logger";
import type { Ref } from "vue";

/**
 * 拖拽事件处理器
 * 专门处理鼠标事件的核心逻辑，包括 mouseDown、mouseMove、mouseUp
 */
export function useDragEventHandlers(
  config: DragHandlerConfig,
  autoScrollConfig: AutoScrollConfig | undefined,
  // 拖拽状态
  isDragging: Ref<boolean>,
  draggedEvent: Ref<RenderableEvent | null>,
  draggingTarget: Ref<DragTarget | null>,
  draggedCurveIndex: Ref<number>,
  dragStartX: Ref<number>,
  dragStartY: Ref<number>,
  longPressTimer: Ref<number | null>,
  // 状态管理函数
  startDragging: () => void,
  resetDragState: () => void,
  // 处理器函数
  handleEventDrag: (currentX: number) => void,
  handleTransientPeakDrag: (currentY: number) => void,
  handleCurvePointDrag: (currentX: number, currentY: number) => void,
  updateStoreInRealTime: () => void,
  handleAutoScroll: (clientX: number, clientY: number) => void,
  executeDragUpdate: (mouseEvent: MouseEvent) => boolean,
  // 辅助函数
  isFrequencyAdjustmentKeyPressed: () => boolean,
  // 临时全局强度管理函数
  disableTemporaryGlobalIntensity: () => void,
  shouldUpdateRealGlobalIntensity: (targetIntensity: number) => boolean,
  calculateNewGlobalIntensity: (targetIntensity: number) => number,
  currentDraggedIntensity: Ref<number>
) {

  /**
   * 处理临时全局强度的同步
   * 在拖拽结束时检查是否需要更新真实的全局强度
   */
  const handleTemporaryGlobalIntensitySync = async (
    draggedEvent: RenderableEvent | null,
    targetType: DragTarget | null
  ): Promise<void> => {
    // 只有在拖拽连续事件的曲线点时才需要处理
    if (!draggedEvent || draggedEvent.type !== "continuous" || targetType !== "continuousCurvePoint") {
      disableTemporaryGlobalIntensity();
      return;
    }

    const continuousEvent = draggedEvent as RenderableContinuousEvent;
    const targetIntensity = currentDraggedIntensity.value;

    // 检查是否需要更新真实的全局强度
    if (shouldUpdateRealGlobalIntensity(targetIntensity)) {
      const newGlobalIntensity = calculateNewGlobalIntensity(targetIntensity);

      dragLogger.info("🎯 拖拽结束，被动更新真实全局强度（保持其他曲线点视觉强度不变）", {
        originalGlobalIntensity: continuousEvent.eventIntensity,
        targetIntensity,
        newGlobalIntensity,
        curveIndex: draggedCurveIndex.value,
        preserveOtherCurveIntensities: true
      });

      // 更新事件的全局强度
      const globalUpdatePayload = {
        Type: "continuous",
        RelativeTime: continuousEvent.startTime,
        Duration: continuousEvent.duration,
        Parameters: {
          Intensity: newGlobalIntensity,
          Frequency: continuousEvent.eventFrequency,
        },
      };

      // 通过Store更新全局强度，使用被动更新模式保持其他曲线点的视觉强度不变
      config.waveformStore.updateSelectedEvent(globalUpdatePayload, {
        preserveOtherCurveIntensities: true
      });

      // 更新本地事件对象的强度值
      continuousEvent.eventIntensity = newGlobalIntensity;
    }

    // 禁用临时全局强度模式
    disableTemporaryGlobalIntensity();
  };

  /**
   * 设置长按计时器
   */
  const setupLongPressTimer = (event: RenderableEvent, targetType: DragTarget, curveIndex?: number) => {
    config.waveformStore.selectEvent(event.id);
    if (targetType === "continuousCurvePoint" && curveIndex !== undefined) {
      config.waveformStore.selectCurvePoint(curveIndex);
    } else {
      config.waveformStore.selectCurvePoint(-1);
    }
    config.smartDrawWaveform(targetType === "event");

    longPressTimer.value = setTimeout(() => {
      if (draggedEvent.value && draggedEvent.value.id === event.id && draggingTarget.value === targetType) {
        startDragging();
        config.invalidateEventCache(event.id);

        // 设置光标样式
        setCursorStyle(targetType, event, curveIndex);
        
        // 添加鼠标移动监听器
        window.addEventListener("mousemove", handleMouseMove);
      }
      longPressTimer.value = null;
    }, config.DRAG_THRESHOLD_MS);
  };

  /**
   * 设置光标样式
   */
  const setCursorStyle = (targetType: DragTarget, event: RenderableEvent, curveIndex?: number) => {
    if (!config.canvas.value) return;

    if (targetType === "transientPeak") {
      config.canvas.value.style.cursor = "ns-resize";
    } else if (targetType === "continuousCurvePoint") {
      // 显示事件tooltip
      if (config.showEventTooltip) {
        const rect = config.canvas.value.getBoundingClientRect();
        const x = dragStartX.value / (window.devicePixelRatio || 1) + rect.left;
        const y = dragStartY.value / (window.devicePixelRatio || 1) + rect.top;
        config.showEventTooltip(x, y);
      }

      if (isFrequencyAdjustmentKeyPressed()) {
        config.canvas.value.style.cursor = "ns-resize";
      } else {
        const continuousEvent = event as RenderableContinuousEvent;
        if (curveIndex === 0) {
          // 第一个Curve点：水平拖拽调整startTime
          config.canvas.value.style.cursor = "ew-resize";
        } else if (curveIndex === continuousEvent.curves.length - 1) {
          // 最后一个Curve点：水平拖拽调整endTime
          config.canvas.value.style.cursor = "ew-resize";
        } else {
          // 中间Curve点：可以移动
          config.canvas.value.style.cursor = "move";
        }
      }
    } else {
      config.canvas.value.style.cursor = "grabbing";
    }
  };

  /**
   * 处理鼠标移动事件（拖拽过程中）
   */
  const handleMouseMove = (mouseEvent: MouseEvent) => {
    if (!isDragging.value || !draggedEvent.value) return;
    mouseEvent.preventDefault();

    const canvas = config.canvas.value;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const currentX = (mouseEvent.clientX - rect.left) * dpr;
    const currentY = (mouseEvent.clientY - rect.top) * dpr;

    // 根据拖动目标类型执行不同的更新逻辑
    if (draggingTarget.value === "event") {
      handleEventDrag(currentX);
    } else if (draggingTarget.value === "transientPeak" && draggedEvent.value?.type === "transient") {
      handleTransientPeakDrag(currentY);
    } else if (draggingTarget.value === "continuousCurvePoint" && draggedEvent.value?.type === "continuous") {
      handleCurvePointDrag(currentX, currentY);

      // 更新tooltip位置
      if (config.updateEventTooltipPosition) {
        const x = mouseEvent.clientX;
        const y = mouseEvent.clientY;
        config.updateEventTooltipPosition(x, y);
      }
    }

    // 实时更新store中的selectedEvent，让EventAdjustPanel能够实时响应
    updateStoreInRealTime();

    // 处理自动滚动
    if (autoScrollConfig) {
      handleAutoScroll(mouseEvent.clientX, mouseEvent.clientY);
    }

    // 使用拖拽专用的重绘函数，确保流畅的拖拽体验
    config.smartDrawWaveform(true);
  };

  /**
   * 计算拖拽距离
   */
  const calculateDragDistance = (mouseEvent: MouseEvent): number => {
    const canvas = config.canvas.value;
    if (!canvas) return 0;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const currentX = (mouseEvent.clientX - rect.left) * dpr;
    const currentY = (mouseEvent.clientY - rect.top) * dpr;
    
    return Math.sqrt(
      Math.pow(currentX - dragStartX.value, 2) +
      Math.pow(currentY - dragStartY.value, 2)
    );
  };

  /**
   * 处理选中状态恢复
   */
  const restoreSelectionState = (eventId: string, curveIndex: number) => {
    setTimeout(() => {
      config.waveformStore.selectEvent(eventId);
      if (curveIndex >= 0) {
        config.waveformStore.selectCurvePoint(curveIndex);
      }
    }, 0);
  };

  /**
   * 处理点击操作（非拖拽）
   */
  const handleClickOperation = (
    currentDraggedEvent: RenderableEvent,
    currentTarget: DragTarget,
    currentCurveIndex: number
  ) => {
    // 保存选中状态信息，用于重置后恢复
    const eventIdToSelect = currentDraggedEvent.id;
    const curveIndexToSelect = currentTarget === "continuousCurvePoint" ? currentCurveIndex : -1;

    // 重置拖动状态
    resetDragState();

    // 延迟恢复选中状态，确保在重置状态后生效
    if (eventIdToSelect) {
      restoreSelectionState(eventIdToSelect, curveIndexToSelect);
    }

    // 强制重绘
    config.smartDrawWaveform(true);
  };

  /**
   * 处理长按但未拖拽的情况
   */
  const handleLongPressWithoutDrag = (
    currentDraggedEvent: RenderableEvent,
    currentTarget: DragTarget,
    currentCurveIndex: number
  ) => {
    // 确保选中状态保持
    if (currentTarget === "continuousCurvePoint" && currentCurveIndex >= 0) {
      // 延迟设置选中状态，确保在重置状态后生效
      setTimeout(() => {
        config.waveformStore.selectEvent(currentDraggedEvent.id);
        config.waveformStore.selectCurvePoint(currentCurveIndex);
      }, 0);
    } else if (currentTarget === "transientPeak" || currentTarget === "event") {
      setTimeout(() => {
        config.waveformStore.selectEvent(currentDraggedEvent.id);
        config.waveformStore.selectCurvePoint(-1);
      }, 0);
    }
    resetDragState();
  };

  /**
   * 处理鼠标释放事件
   */
  const handleMouseUp = async (mouseEvent: MouseEvent) => {
    // 清除长按计时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }

    const wasDragging = isDragging.value;
    const currentDraggedEvent = draggedEvent.value;
    const currentTarget = draggingTarget.value;
    const currentCurveIndex = draggedCurveIndex.value;

    // 如果不是拖动状态，但有拖拽目标（长按但没有实际拖拽的情况）
    if (!wasDragging && currentDraggedEvent && currentTarget) {
      handleLongPressWithoutDrag(currentDraggedEvent, currentTarget, currentCurveIndex);
      return;
    }

    // 如果不是拖动状态且没有拖拽目标，直接重置状态
    if (!wasDragging) {
      resetDragState();
      return;
    }

    // 检查是否有实际的位置变化（拖拽距离超过阈值）
    const DRAG_THRESHOLD = 3; // 像素阈值，小于此值认为是点击而非拖拽
    const dragDistance = calculateDragDistance(mouseEvent);

    // 如果拖拽距离小于阈值，认为是点击操作，不执行更新
    if (dragDistance < DRAG_THRESHOLD) {
      dragLogger.debug(`检测到点击操作（拖拽距离: ${dragDistance.toFixed(1)}px < ${DRAG_THRESHOLD}px），跳过更新操作`);

      if (currentDraggedEvent && currentTarget) {
        handleClickOperation(currentDraggedEvent, currentTarget, currentCurveIndex);
      }
      return;
    }

    // 拖动操作结束处理
    if (!currentDraggedEvent || !currentTarget) {
      resetDragState();
      return;
    }

    mouseEvent.preventDefault();
    dragLogger.debug(`检测到真实拖拽操作（拖拽距离: ${dragDistance.toFixed(1)}px），执行更新操作`);

    try {
      // 在执行拖拽更新之前，处理临时全局强度的同步
      await handleTemporaryGlobalIntensitySync(currentDraggedEvent, currentTarget);

      // 执行拖拽更新
      const updateSuccess = executeDragUpdate(mouseEvent);

      if (!updateSuccess) {
        dragLogger.warn("拖拽更新未成功执行");
      }
    } catch (error) {
      dragLogger.error("拖拽更新失败", error);
    } finally {
      // 保存选中状态信息，用于重置后恢复
      const eventIdToSelect = currentDraggedEvent.id;
      const curveIndexToSelect = currentTarget === "continuousCurvePoint" ? currentCurveIndex : -1;

      // 重置拖动状态
      resetDragState();

      // 延迟恢复选中状态，确保在重置状态后生效
      restoreSelectionState(eventIdToSelect, curveIndexToSelect);

      // 强制重绘
      config.smartDrawWaveform(true);
    }
  };

  /**
   * 清理事件监听器
   */
  const cleanup = () => {
    // 移除鼠标移动监听器
    window.removeEventListener("mousemove", handleMouseMove);
    
    // 清除长按计时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
  };

  return {
    // 主要事件处理器
    handleMouseMove,
    handleMouseUp,
    
    // 辅助函数
    setupLongPressTimer,
    setCursorStyle,
    calculateDragDistance,
    restoreSelectionState,
    
    // 清理函数
    cleanup,
  };
}
