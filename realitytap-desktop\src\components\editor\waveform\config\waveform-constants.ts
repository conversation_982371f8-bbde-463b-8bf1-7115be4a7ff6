/**
 * 波形图相关常量配置
 * 统一管理所有波形绘制相关的常量，避免重复定义
 */

// ===== 波形垂直偏移配置 =====

/**
 * Event波形垂直偏移量（像素）
 * 用于确保X轴（零基线）可见，避免被Event波形遮挡
 *
 * 注意：此值会影响以下波形的绘制：
 * - 瞬态事件波形
 * - 连续事件波形
 * - 音频振幅波形
 *
 * 修改此值时需要确保所有波形类型都使用相同的偏移量，
 * 以保持视觉一致性
 */
export const EVENT_WAVEFORM_VERTICAL_OFFSET = 1; // 像素

// ===== 波形绘制样式常量 =====

/**
 * 默认线宽
 */
export const DEFAULT_WAVEFORM_LINE_WIDTH = 1.5;

/**
 * 选中状态线宽
 */
export const SELECTED_WAVEFORM_LINE_WIDTH = 2;

/**
 * 默认点半径
 */
export const DEFAULT_WAVEFORM_POINT_RADIUS = 3;

/**
 * 选中状态点半径
 */
export const SELECTED_WAVEFORM_POINT_RADIUS = 5.5;

// ===== 波形交互常量 =====

/**
 * 点击检测半径（像素）
 * 用于判断鼠标点击是否命中波形上的点
 */
export const WAVEFORM_CLICK_RADIUS = 8;

/**
 * 拖拽阈值时间（毫秒）
 * 长按超过此时间才开始拖拽操作
 * 优化：从200ms减少到50ms，提高拖拽响应性，减少"卡顿"感
 */
export const WAVEFORM_DRAG_THRESHOLD_MS = 50;

// ===== 波形渲染性能常量 =====

/**
 * 视口裁剪容差（像素）
 * 超出可见区域此距离的波形元素将不被绘制
 */
export const WAVEFORM_VIEWPORT_TOLERANCE = 50;

/**
 * 最大可见事件数量
 * 限制同时绘制的事件数量以保证性能
 */
export const MAX_VISIBLE_EVENTS = 100;

/**
 * 事件缓存最大数量
 * 连续事件坐标缓存的最大条目数
 */
export const MAX_EVENT_CACHE_SIZE = 50;

// ===== 波形布局常量 =====

/**
 * 画布安全偏移量（像素）
 * 用于虚拟滚动时的坐标计算
 */
export const WAVEFORM_SAFE_OFFSET = 15;

/**
 * 滚动边界容差（像素）
 * 用于解决浮点数精度问题和判断是否接近滚动边界
 */
export const SCROLL_BOUNDARY_TOLERANCE = 0.5;

/**
 * 布局相关常量
 * 统一管理布局计算中使用的数值
 */
export const LAYOUT_CONSTANTS = {
  /** 右侧padding（像素） */
  RIGHT_PADDING: 10,
  /** 默认padding配置 */
  DEFAULT_PADDING: {
    top: 10,
    right: 20,
    bottom: 30,
    left: 40
  }
} as const;

/**
 * 坐标计算精度配置
 * 统一管理坐标转换中的精度处理
 */
export const COORDINATE_PRECISION = {
  /** 虚拟偏移量精度倍数（保留1位小数） */
  VIRTUAL_OFFSET_MULTIPLIER: 10,
  /** 虚拟偏移量小数位数 */
  VIRTUAL_OFFSET_DECIMAL_PLACES: 1,
  /** 时间坐标精度（是否取整） */
  TIME_COORDINATE_ROUND: true
} as const;

// ===== 事件创建常量 =====

/**
 * 最小瞬态事件持续时间（毫秒）
 */
export const MIN_TRANSIENT_DURATION = 8;

/**
 * 最小连续事件持续时间（毫秒）
 */
export const MIN_CONTINUOUS_DURATION = 25;

/**
 * 连续事件 Curve 点之间的最小时间间隔（毫秒）
 *
 * 用于确保相邻 Curve 点之间保持足够的时间间隔，防止：
 * - Curve 点重叠导致的时间序列混乱
 * - 拖拽操作时的边界检测错误
 * - 事件播放时的时间精度问题
 *
 * 影响范围：
 * - Curve 点拖拽时的边界约束计算
 * - 事件时长调整时的 Curve 点重新分布
 * - 事件预览时的时间验证逻辑
 *
 * 注意：此值必须为正整数，符合 RealityTap Studio 时间值要求
 */
export const MIN_CURVE_POINT_INTERVAL_MS = 1;

/**
 * Event波形之间的最小间隔时间（毫秒）
 *
 * 用于确保不同Event波形之间保持足够的时间间隔，防止：
 * - Event波形在时间轴上过于紧密排列
 * - 拖拽操作时的精确控制困难
 * - 事件播放时的时间冲突问题
 * - 用户界面上的视觉混乱
 *
 * 影响范围：
 * - Event拖拽时的边界约束计算
 * - 添加新Event时的位置验证
 * - Event碰撞检测逻辑
 * - 可用空间计算
 *
 * 应用场景：
 * - 拖拽Event时，确保与相邻Event保持此最小间隔
 * - 添加新Event时，验证与现有Event的间隔
 * - 所有Event类型（瞬态和连续）都适用此间隔要求
 *
 * 注意：此值必须为正整数，符合 RealityTap Studio 时间值要求
 */
export const MIN_EVENT_INTERVAL_MS = 0

// ===== 音频波形常量 =====

/**
 * 音频波形默认振幅缩放比例
 * 表示音频波形占用画布高度的百分比
 */
export const DEFAULT_AUDIO_AMPLITUDE_SCALE = 0.75;

/**
 * 音频波形默认振幅增强倍数
 * 用于放大小幅度信号的可见性
 */
export const DEFAULT_AUDIO_AMPLITUDE_BOOST = 1.5;

/**
 * 音频波形无声阈值百分比
 * 相对于最大振幅的百分比，低于此值的信号被视为无声
 */
export const DEFAULT_AUDIO_SILENCE_THRESHOLD_PERCENT = 2;

/**
 * 最大允许的Haptic时长（毫秒）
 *
 * 默认值：30000ms (30秒)
 * 可根据项目需求调整此值
 */
export const MAX_HAPTIC_DURATION_MS = 30000;

// ===== 强度映射常量 =====

/**
 * 强度值范围配置
 * 统一管理所有强度相关的数值范围，确保一致性
 */
export const INTENSITY_RANGE = {
  /** 最小强度值 */
  MIN: 0,
  /** 最大强度值 */
  MAX: 100
} as const;

/**
 * 强度计算精度配置
 */
export const INTENSITY_PRECISION = {
  /** 强度值小数位数 */
  DECIMAL_PLACES: 0,
  /** 原始强度比例小数位数 */
  RAW_RATIO_DECIMAL_PLACES: 2
} as const;

// ===== 频率映射常量 =====

/**
 * 最小频率值
 */
export const MIN_FREQUENCY = -100;

/**
 * 最大频率值
 */
export const MAX_FREQUENCY = 200;

// ===== 缩放功能常量 =====

/**
 * 默认缩放级别
 */
export const DEFAULT_ZOOM_LEVEL = 1.0;

/**
 * 最大缩放级别
 */
export const MAX_ZOOM_LEVEL = 10.0;

/**
 * 缩放步进值
 */
export const ZOOM_STEP = 0.1;

// ===== 快捷键配置常量 =====

/**
 * 频率调节快捷键配置
 * 用于在拖拽曲线点时调节频率的修饰键
 */
export const FREQUENCY_ADJUSTMENT_KEY = 'Control';

/**
 * 支持的修饰键列表
 * 可选值：'Alt', 'Control', 'Shift', 'Meta'
 */
export const SUPPORTED_MODIFIER_KEYS = ['Alt', 'Control', 'Shift', 'Meta'] as const;

/**
 * 修饰键显示名称映射
 * 用于在UI中显示用户友好的按键名称
 */
export const MODIFIER_KEY_DISPLAY_NAMES = {
  Alt: 'Alt',
  Control: 'Ctrl',
  Shift: 'Shift',
  Meta: 'Cmd'
} as const;

/**
 * 获取当前配置的频率调节快捷键的显示名称
 */
export const getFrequencyAdjustmentKeyDisplayName = (): string => {
  return MODIFIER_KEY_DISPLAY_NAMES[FREQUENCY_ADJUSTMENT_KEY as keyof typeof MODIFIER_KEY_DISPLAY_NAMES] || FREQUENCY_ADJUSTMENT_KEY;
};

/**
 * 缩放灵敏度（每次滚轮事件的缩放变化量）
 */
export const ZOOM_SENSITIVITY = 0.1;

/**
 * 缩放平滑过渡时间（毫秒）
 */
export const ZOOM_TRANSITION_DURATION = 150;

/**
 * 全屏时长阈值（毫秒）
 * 当总时长小于等于此值时：
 * 1. 内容铺满父元素宽度，不显示水平滚动条
 * 2. 禁用缩放功能
 * 当总时长大于此值时：
 * 1. 在默认缩放率下显示水平滚动条
 * 2. 允许缩放功能
 */
export const FULL_SCREEN_TIME_MS = 500;

/**
 * 最大可视时长倍数
 * 用于限制最小缩放率，防止可视范围内显示过多元素导致性能问题
 *
 * 计算公式：最小缩放率 = 可视范围宽度 / (FULL_SCREEN_TIME_MS * maxVisibleTimeMultiplier * 时间像素比)
 *
 * 当缩放率过小时，可视范围内会显示过多的时间范围，导致：
 * - 绘制过多的事件和网格线
 * - 渲染性能下降
 * - 用户体验变差
 *
 * 默认值 3 表示：可视范围内最多显示 FULL_SCREEN_TIME_MS * 3 = 1500ms 的内容
 */
export const MAX_VISIBLE_TIME_MULTIPLIER = 4;

/**
 * 可视范围内的最小刻度数量
 * 防止刻度过少导致时间定位困难
 */
export const MIN_VISIBLE_TICK_COUNT = 10;

/**
 * 可视范围内的最大刻度数量
 * 防止刻度过密导致标签重叠
 */
export const MAX_VISIBLE_TICK_COUNT = 15;

/**
 * 时间刻度终点距离优化相关常量
 */

/**
 * 最小终点余数比例
 * 当 effectiveDuration % timeStep 的比例小于此值时，认为终点刻度过于接近前一个刻度
 * 默认值 0.25 表示：如果余数小于步进值的25%，则认为过于接近
 */
export const MIN_END_REMAINDER_RATIO = 0.35;

/**
 * 最大终点余数比例
 * 当 effectiveDuration % timeStep 的比例大于此值时，认为终点刻度距离合适
 * 默认值 0.75 表示：如果余数大于步进值的75%，则认为距离合适
 */
export const MAX_END_REMAINDER_RATIO = 0.75;

/**
 * 步进值调整容差
 * 在寻找最优步进值时，允许的步进值调整幅度（相对于原始计算值的比例）
 * 默认值 0.2 表示：允许步进值在原始值的±20%范围内调整
 */
export const STEP_ADJUSTMENT_TOLERANCE = 0.2;

/**
 * 启用终点距离优化
 * 控制是否启用新的终点距离优化算法
 * 设为 false 可以回退到原始算法
 */
export const ENABLE_END_DISTANCE_OPTIMIZATION = true;

/**
 * 像素驱动时间刻度算法相关常量
 *
 * 这些常量控制新的像素驱动算法的行为，该算法通过基于像素距离的计算
 * 来确保时间刻度线的视觉分布更加均匀和美观。
 *
 * 核心思想：
 * - 传统算法基于时间范围计算刻度，可能导致像素分布不均
 * - 新算法基于像素距离计算时间步进，确保视觉一致性
 * - 通过近似整除机制，在保持美观的同时提供足够的灵活性
 */

/**
 * 最小像素距离（像素）
 * 两根时间刻度线之间的最小像素距离
 *
 * 作用：
 * - 确保时间标签不会重叠
 * - 保持良好的可读性
 * - 避免刻度过于密集
 */
export const MIN_PIXEL_DISTANCE = 160;

/**
 * 最大像素距离（像素）
 * 两根时间刻度线之间的最大像素距离
 *
 * 作用：
 * - 确保有足够的刻度密度
 * - 便于精确的时间定位
 * - 避免刻度过于稀疏
 *
 * 建议值：
 * - 通常为最小距离的2倍
 * - 根据应用场景调整
 */
export const MAX_PIXEL_DISTANCE = 220;

/**
 * 理想像素距离（像素）
 * 两根时间刻度线之间的理想像素距离
 *
 * 作用：
 * - 用于评分算法中的刻度数量合理性计算
 * - 作为算法优化的目标值
 * - 平衡密度和可读性
 *
 * 计算：
 * - 通常为最小和最大距离的中间值
 * - 可根据用户体验调整
 */
export const IDEAL_PIXEL_DISTANCE = 180;

/**
 * 像素整除容差
 * 允许的近似整除容差比例
 *
 * 作用：
 * - 0.05 表示5%的容差
 * - 余数在5%以内或95%以上时认为是近似整除
 * - 增加算法的灵活性，避免过于严格的整除要求
 *
 * 调整指南：
 * - 较小值（0.01-0.03）：更严格的整除要求，视觉效果更佳
 * - 较大值（0.05-0.1）：更灵活的匹配，适用性更广
 */
export const PIXEL_DIVISIBILITY_TOLERANCE = 0.05;

/**
 * 启用像素驱动算法
 * 控制是否启用新的像素驱动时间刻度算法
 *
 * 作用：
 * - true：使用新的像素驱动算法（推荐）
 * - false：回退到原有的时间驱动算法
 *
 * 使用场景：
 * - 开发阶段：可设为false进行对比测试
 * - 生产环境：建议设为true以获得最佳视觉效果
 * - 故障排除：可临时设为false排查问题
 */
export const ENABLE_PIXEL_BASED_ALGORITHM = true;

/**
 * 波形垂直偏移相关常量
 */
export const WAVEFORM_OFFSET_CONSTANTS = {
  EVENT_WAVEFORM_VERTICAL_OFFSET,
} as const;

/**
 * 波形样式相关常量
 */
export const WAVEFORM_STYLE_CONSTANTS = {
  DEFAULT_WAVEFORM_LINE_WIDTH,
  SELECTED_WAVEFORM_LINE_WIDTH,
  DEFAULT_WAVEFORM_POINT_RADIUS,
  SELECTED_WAVEFORM_POINT_RADIUS,
} as const;

/**
 * 波形交互相关常量
 */
export const WAVEFORM_INTERACTION_CONSTANTS = {
  WAVEFORM_CLICK_RADIUS,
  WAVEFORM_DRAG_THRESHOLD_MS,
} as const;

/**
 * 波形性能相关常量
 */
export const WAVEFORM_PERFORMANCE_CONSTANTS = {
  WAVEFORM_VIEWPORT_TOLERANCE,
  MAX_VISIBLE_EVENTS,
  MAX_EVENT_CACHE_SIZE,
} as const;

/**
 * 波形布局相关常量
 */
export const WAVEFORM_LAYOUT_CONSTANTS = {
  WAVEFORM_SAFE_OFFSET,
  SCROLL_BOUNDARY_TOLERANCE,
} as const;

/**
 * 事件创建相关常量
 */
export const EVENT_CREATION_CONSTANTS = {
  MIN_TRANSIENT_DURATION,
  MIN_CONTINUOUS_DURATION,
  MIN_CURVE_POINT_INTERVAL_MS,
  MIN_EVENT_INTERVAL_MS,
} as const;

/**
 * 音频波形相关常量
 */
export const AUDIO_WAVEFORM_CONSTANTS = {
  DEFAULT_AUDIO_AMPLITUDE_SCALE,
  DEFAULT_AUDIO_AMPLITUDE_BOOST,
  DEFAULT_AUDIO_SILENCE_THRESHOLD_PERCENT,
  MAX_HAPTIC_DURATION_MS: MAX_HAPTIC_DURATION_MS,
} as const;

/**
 * 缩放调试配置
 * 设置为 true 可以在控制台看到详细的缩放操作日志
 */
export const ZOOM_DEBUG_MODE = false;

/**
 * X轴刻度相关常量
 */
export const TICK_CONSTANTS = {
  MIN_VISIBLE_TICK_COUNT,
  MAX_VISIBLE_TICK_COUNT,
  MIN_END_REMAINDER_RATIO,
  MAX_END_REMAINDER_RATIO,
  STEP_ADJUSTMENT_TOLERANCE,
  ENABLE_END_DISTANCE_OPTIMIZATION,
  // 像素驱动算法相关常量
  MIN_PIXEL_DISTANCE,
  MAX_PIXEL_DISTANCE,
  IDEAL_PIXEL_DISTANCE,
  PIXEL_DIVISIBILITY_TOLERANCE,
  ENABLE_PIXEL_BASED_ALGORITHM,
} as const;

/**
 * 快捷键相关常量
 */
export const KEYBOARD_SHORTCUT_CONSTANTS = {
  FREQUENCY_ADJUSTMENT_KEY,
  SUPPORTED_MODIFIER_KEYS,
  MODIFIER_KEY_DISPLAY_NAMES,
  getFrequencyAdjustmentKeyDisplayName,
} as const;

/**
 * 缩放功能相关常量
 */
export const ZOOM_CONSTANTS = {
  DEFAULT_ZOOM_LEVEL,
  MAX_ZOOM_LEVEL,
  ZOOM_STEP,
  ZOOM_SENSITIVITY,
  ZOOM_TRANSITION_DURATION,
  FULL_SCREEN_TIME_MS,
  MAX_VISIBLE_TIME_MULTIPLIER,
  ZOOM_DEBUG_MODE,
} as const;

/**
 * 强度映射相关常量
 */
export const INTENSITY_CONSTANTS = {
  INTENSITY_RANGE,
  INTENSITY_PRECISION,
} as const;

/**
 * 频率映射相关常量
 */
export const FREQUENCY_CONSTANTS = {
  MIN_FREQUENCY,
  MAX_FREQUENCY,
} as const;

/**
 * 坐标计算相关常量
 */
export const COORDINATE_CONSTANTS = {
  COORDINATE_PRECISION,
  LAYOUT_CONSTANTS,
} as const;

// ===== 拖拽自动滚动常量 =====

/**
 * 自动滚动触发阈值（像素）
 * 当拖拽元素距离容器边界小于此值时开始自动滚动
 */
export const AUTO_SCROLL_TRIGGER_THRESHOLD = 50;

/**
 * 自动滚动基础速度（像素/次）
 * 距离边界较远时的滚动速度
 */
export const AUTO_SCROLL_BASE_SPEED = 20;

/**
 * 自动滚动最大速度（像素/次）
 * 距离边界很近时的最大滚动速度
 */
export const AUTO_SCROLL_MAX_SPEED = 100;

/**
 * 自动滚动节流时间（毫秒）
 * 控制自动滚动的执行频率，约60fps
 */
export const AUTO_SCROLL_THROTTLE_MS = 16;

/**
 * 自动滚动速度计算因子
 * 用于根据距离边界的距离计算滚动速度
 * 值越大，速度变化越敏感
 */
export const AUTO_SCROLL_SPEED_FACTOR = 2;

/**
 * 拖拽自动滚动相关常量
 */
export const AUTO_SCROLL_CONSTANTS = {
  AUTO_SCROLL_TRIGGER_THRESHOLD,
  AUTO_SCROLL_BASE_SPEED,
  AUTO_SCROLL_MAX_SPEED,
  AUTO_SCROLL_THROTTLE_MS,
  AUTO_SCROLL_SPEED_FACTOR,
} as const;

// ===== 调试实验配置常量 =====

/**
 * 调试实验开关配置
 * 用于系统性排查 Event 波形拖拽时其它波形闪烁的问题
 *
 * 使用方法：
 * - 将对应实验设置为 true 来启用该实验
 * - 将对应实验设置为 false 来禁用该实验（使用原始逻辑）
 * - 一次只启用一个实验，以便准确定位问题
 *
 * 注意：生产环境下，所有调试日志将被自动禁用以提升性能
 */
export const DEBUG_EXPERIMENTS = {
  /**
   * 实验1: 禁用拖拽分层优化
   * 禁用拖拽时的静态/动态事件分层绘制优化，统一使用标准绘制
   */
  DISABLE_DRAG_LAYER_OPTIMIZATION: false,

  /**
   * 实验2: 禁用绘制缓存机制
   * 禁用所有绘制缓存检查，强制每次都重新绘制
   */
  DISABLE_DRAW_CACHE: false,

  /**
   * 实验3: 禁用事件绘制缓存
   * 禁用连续事件的坐标缓存，每次都重新计算坐标
   */
  DISABLE_EVENT_DRAW_CACHE: false,

  /**
   * 实验4: 禁用节流机制
   * 禁用重绘函数的节流优化，直接调用主绘制函数
   */
  DISABLE_THROTTLING: false,

  /**
   * 实验5: 禁用性能监控
   * 禁用绘制性能统计和监控
   */
  DISABLE_PERFORMANCE_MONITORING: false,

  /**
   * 实验6: 禁用事件变化检测
   * 禁用事件数据变化的精确检测，总是认为有变化
   */
  DISABLE_EVENT_CHANGE_DETECTION: false,

  /**
   * 实验7: 禁用辅助线绘制
   * 禁用拖拽时的辅助虚线绘制
   */
  DISABLE_GUIDE_LINES: false,

  /**
   * 实验8: 禁用音频波形绘制
   * 禁用音频波形的绘制，只绘制事件波形
   */
  DISABLE_AUDIO_WAVEFORM: false,
} as const;

/**
 * 调试实验相关常量
 */
export const DEBUG_CONSTANTS = {
  DEBUG_EXPERIMENTS,
} as const;

/**
 * 所有波形常量的集合
 */
export const ALL_WAVEFORM_CONSTANTS = {
  ...WAVEFORM_OFFSET_CONSTANTS,
  ...WAVEFORM_STYLE_CONSTANTS,
  ...WAVEFORM_INTERACTION_CONSTANTS,
  ...WAVEFORM_PERFORMANCE_CONSTANTS,
  ...WAVEFORM_LAYOUT_CONSTANTS,
  ...EVENT_CREATION_CONSTANTS,
  ...AUDIO_WAVEFORM_CONSTANTS,
  ...TICK_CONSTANTS,
  ...ZOOM_CONSTANTS,
  ...INTENSITY_CONSTANTS,
  ...FREQUENCY_CONSTANTS,
  ...COORDINATE_CONSTANTS,
  ...AUTO_SCROLL_CONSTANTS,
  ...DEBUG_CONSTANTS,
} as const;
