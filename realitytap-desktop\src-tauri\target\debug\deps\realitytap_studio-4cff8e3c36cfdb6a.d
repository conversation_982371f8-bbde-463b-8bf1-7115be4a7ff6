E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\deps\librealitytap_studio-4cff8e3c36cfdb6a.rmeta: src\lib.rs src\commands\mod.rs src\commands\app_config.rs src\commands\app_data.rs src\commands\project.rs src\commands\file.rs src\commands\group.rs src\commands\utility.rs src\commands\version.rs src\commands\process_manager.rs src\commands\ota_config.rs src\commands\debug.rs src\error.rs src\models\mod.rs src\models\project.rs src\models\file.rs src\models\group.rs src\models\audio.rs src\models\window.rs src\models\common.rs src\models\device.rs src\project\mod.rs src\project\io.rs src\project\generator.rs src\project\operations.rs src\audio\mod.rs src\audio\formats.rs src\audio\analysis.rs src\audio\commands.rs src\audio\ffmpeg.rs src\utils\mod.rs src\utils\constants.rs src\utils\validation.rs src\utils\directory_walker.rs src\utils\temp_file.rs src\device\mod.rs src\device\manager.rs src\device\usb_device.rs src\device\wifi_device.rs src\device\bluetooth_device.rs src\device\transmission.rs src\device\commands.rs src\device\discovery.rs src\haptic\mod.rs src\haptic\error.rs src\haptic\ffi.rs src\haptic\handler.rs src\haptic\lifecycle.rs src\haptic\commands.rs E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out/3d93c4ee46d1ca28ba179957f643ab3cacdd42ed0ee61c33ac687f5d2caa885d

E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\deps\librealitytap_studio-4cff8e3c36cfdb6a.rlib: src\lib.rs src\commands\mod.rs src\commands\app_config.rs src\commands\app_data.rs src\commands\project.rs src\commands\file.rs src\commands\group.rs src\commands\utility.rs src\commands\version.rs src\commands\process_manager.rs src\commands\ota_config.rs src\commands\debug.rs src\error.rs src\models\mod.rs src\models\project.rs src\models\file.rs src\models\group.rs src\models\audio.rs src\models\window.rs src\models\common.rs src\models\device.rs src\project\mod.rs src\project\io.rs src\project\generator.rs src\project\operations.rs src\audio\mod.rs src\audio\formats.rs src\audio\analysis.rs src\audio\commands.rs src\audio\ffmpeg.rs src\utils\mod.rs src\utils\constants.rs src\utils\validation.rs src\utils\directory_walker.rs src\utils\temp_file.rs src\device\mod.rs src\device\manager.rs src\device\usb_device.rs src\device\wifi_device.rs src\device\bluetooth_device.rs src\device\transmission.rs src\device\commands.rs src\device\discovery.rs src\haptic\mod.rs src\haptic\error.rs src\haptic\ffi.rs src\haptic\handler.rs src\haptic\lifecycle.rs src\haptic\commands.rs E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out/3d93c4ee46d1ca28ba179957f643ab3cacdd42ed0ee61c33ac687f5d2caa885d

E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\deps\realitytap_studio-4cff8e3c36cfdb6a.d: src\lib.rs src\commands\mod.rs src\commands\app_config.rs src\commands\app_data.rs src\commands\project.rs src\commands\file.rs src\commands\group.rs src\commands\utility.rs src\commands\version.rs src\commands\process_manager.rs src\commands\ota_config.rs src\commands\debug.rs src\error.rs src\models\mod.rs src\models\project.rs src\models\file.rs src\models\group.rs src\models\audio.rs src\models\window.rs src\models\common.rs src\models\device.rs src\project\mod.rs src\project\io.rs src\project\generator.rs src\project\operations.rs src\audio\mod.rs src\audio\formats.rs src\audio\analysis.rs src\audio\commands.rs src\audio\ffmpeg.rs src\utils\mod.rs src\utils\constants.rs src\utils\validation.rs src\utils\directory_walker.rs src\utils\temp_file.rs src\device\mod.rs src\device\manager.rs src\device\usb_device.rs src\device\wifi_device.rs src\device\bluetooth_device.rs src\device\transmission.rs src\device\commands.rs src\device\discovery.rs src\haptic\mod.rs src\haptic\error.rs src\haptic\ffi.rs src\haptic\handler.rs src\haptic\lifecycle.rs src\haptic\commands.rs E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out/3d93c4ee46d1ca28ba179957f643ab3cacdd42ed0ee61c33ac687f5d2caa885d

src\lib.rs:
src\commands\mod.rs:
src\commands\app_config.rs:
src\commands\app_data.rs:
src\commands\project.rs:
src\commands\file.rs:
src\commands\group.rs:
src\commands\utility.rs:
src\commands\version.rs:
src\commands\process_manager.rs:
src\commands\ota_config.rs:
src\commands\debug.rs:
src\error.rs:
src\models\mod.rs:
src\models\project.rs:
src\models\file.rs:
src\models\group.rs:
src\models\audio.rs:
src\models\window.rs:
src\models\common.rs:
src\models\device.rs:
src\project\mod.rs:
src\project\io.rs:
src\project\generator.rs:
src\project\operations.rs:
src\audio\mod.rs:
src\audio\formats.rs:
src\audio\analysis.rs:
src\audio\commands.rs:
src\audio\ffmpeg.rs:
src\utils\mod.rs:
src\utils\constants.rs:
src\utils\validation.rs:
src\utils\directory_walker.rs:
src\utils\temp_file.rs:
src\device\mod.rs:
src\device\manager.rs:
src\device\usb_device.rs:
src\device\wifi_device.rs:
src\device\bluetooth_device.rs:
src\device\transmission.rs:
src\device\commands.rs:
src\device\discovery.rs:
src\haptic\mod.rs:
src\haptic\error.rs:
src\haptic\ffi.rs:
src\haptic\handler.rs:
src\haptic\lifecycle.rs:
src\haptic\commands.rs:
E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out/3d93c4ee46d1ca28ba179957f643ab3cacdd42ed0ee61c33ac687f5d2caa885d:

# env-dep:APP_VERSION
# env-dep:BUILD_DATE=2025-08-05 00:29:45 UTC
# env-dep:CARGO_PKG_AUTHORS=AWA Customer Support Team
# env-dep:CARGO_PKG_DESCRIPTION=AWA RealityTap Studio Main Application
# env-dep:CARGO_PKG_NAME=realitytap_studio
# env-dep:CARGO_PKG_VERSION=1.0.8
# env-dep:OUT_DIR=E:\\03.Codes\\10.AWA\\10.realitytap\\realitytap-ecosystem\\realitytap-desktop\\src-tauri\\target\\debug\\build\\realitytap_studio-f368511d71eb7527\\out
# env-dep:TARGET_ARCH=x86_64
# env-dep:TARGET_OS=windows
