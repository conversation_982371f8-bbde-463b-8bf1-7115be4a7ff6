import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import { SELECTED_POINT_RADIUS } from "../utils/drawing-helpers";
import { hitTestEvents } from "../utils/hit-test-service";
import { useDragState } from "./useDragState";
import { type DragHandlerConfig, type AutoScrollConfig } from "./useDragConfig";
import { useDragPreview } from "./useDragPreview";
import { useDragStoreUpdater } from "./useDragStoreUpdater";
import { useDragAutoScroll } from "./useDragAutoScroll";
import { useDragBoundaryValidator } from "./useDragBoundaryValidator";
import { useDragUpdateManager } from "./useDragUpdateManager";
import { useTransientDragProcessor } from "./useTransientDragProcessor";
import { useContinuousDragProcessor } from "./useContinuousDragProcessor";
import { useDragEventHandlers } from "./useDragEventHandlers";
import { dragLogger } from "@/utils/logger/logger";

/**
 * 拖拽处理器主控制器
 * 整合所有子处理器，提供统一的拖拽处理接口
 */
export function useDragHandler(config: DragHandlerConfig, autoScrollConfig?: AutoScrollConfig) {
  // 辅助函数：动态获取事件数据
  const getEvents = (): RenderableEvent[] => {
    return typeof config.events === "function" ? config.events() : config.events;
  };

  // 辅助函数：检查频率调节键是否被按下
  const isFrequencyAdjustmentKeyPressed = () => {
    return config.isFrequencyAdjustmentKeyPressed.value;
  };

  // 初始化拖拽状态管理
  const dragState = useDragState();
  const {
    isDragging,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartX,
    dragStartY,
    dragStartEventTime,
    longPressTimer,
    currentDraggedIntensity,
    currentDraggedTimeOffset,
    currentDraggedRawIntensity,
    currentDraggedFrequency,
    currentDraggedRelativeFrequency,
    resetDragState: resetDragStateBase,
    initializeDragState,
    setupTransientPeakDrag,
    setupEventDrag,
    setupCurvePointDrag,
    startDragging,
    updateEventDragValues,
    updateTransientPeakDragValues,
    updateCurvePointFrequencyDragValues,
    updateCurvePointPositionDragValues,
    // 临时全局强度管理函数
    enableTemporaryGlobalIntensity,
    disableTemporaryGlobalIntensity,
    getCurrentEffectiveGlobalIntensity,
    shouldUpdateRealGlobalIntensity,
    calculateNewGlobalIntensity,
    // 临时全局强度状态查询
    isUsingTemporaryGlobalIntensity,
    originalGlobalIntensity,
  } = dragState;

  // 初始化更新管理器
  const updateManager = useDragUpdateManager(
    config,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartEventTime,
    dragStartX,
    currentDraggedIntensity,
    currentDraggedTimeOffset,
    currentDraggedRelativeFrequency,
    isFrequencyAdjustmentKeyPressed,
    getEvents
  );
  const { executeDragUpdate } = updateManager;

  // 初始化瞬态事件处理器
  const transientProcessor = useTransientDragProcessor(
    config,
    draggedEvent,
    draggingTarget,
    currentDraggedIntensity,
    setupTransientPeakDrag,
    setupEventDrag,
    updateTransientPeakDragValues
  );
  const { handleTransientMouseDown, handleTransientPeakDrag } = transientProcessor;

  // 初始化连续事件处理器
  const continuousProcessor = useContinuousDragProcessor(
    config,
    draggedEvent,
    draggedCurveIndex,
    dragStartX,
    dragStartY,
    currentDraggedTimeOffset,
    setupCurvePointDrag,
    setupEventDrag,
    updateEventDragValues,
    updateCurvePointFrequencyDragValues,
    updateCurvePointPositionDragValues,
    isFrequencyAdjustmentKeyPressed,
    getEvents,
    // 临时全局强度管理函数
    enableTemporaryGlobalIntensity,
    disableTemporaryGlobalIntensity,
    getCurrentEffectiveGlobalIntensity,
    shouldUpdateRealGlobalIntensity,
    calculateNewGlobalIntensity
  );
  const { handleContinuousMouseDown, handleCurvePointDrag } = continuousProcessor;

  // 初始化拖拽预览
  const dragPreview = useDragPreview(
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    currentDraggedTimeOffset,
    currentDraggedIntensity,
    currentDraggedRelativeFrequency,
    config.isFrequencyAdjustmentKeyPressed,
    getCurrentEffectiveGlobalIntensity
  );
  const { createPreviewEvent } = dragPreview;

  // 初始化Store更新器
  const dragStoreUpdater = useDragStoreUpdater(
    config.waveformStore,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartEventTime,
    currentDraggedTimeOffset,
    currentDraggedIntensity,
    currentDraggedRelativeFrequency,
    config.isFrequencyAdjustmentKeyPressed,
    (time: number) => Math.floor(Math.max(0, time)) // floorTime 函数
  );
  const { updateStoreInRealTime } = dragStoreUpdater;

  // 初始化自动滚动
  const dragAutoScroll = useDragAutoScroll(autoScrollConfig, draggingTarget, draggedEvent, dragStartEventTime, currentDraggedTimeOffset);
  const { handleAutoScroll } = dragAutoScroll;

  // 处理整个事件的拖拽
  const handleEventDrag = (currentX: number) => {
    const deltaX = currentX - dragStartX.value;
    const timeOffset = config.mapXOffsetToTimeOffsetLocal(deltaX);
    let newStartTime = dragStartEventTime.value + timeOffset;

    // 使用边界验证器进行验证
    const boundaryValidator = useDragBoundaryValidator();
    const validation = boundaryValidator.validateEventDragBoundary(
      draggedEvent.value!,
      newStartTime,
      getEvents(),
      config.waveformStore.totalDuration,
      config.waveformStore.isDurationLockedByAudio
    );

    // 处理时长自动扩展
    if (validation.needsTimeExtension && validation.suggestedTotalDuration) {
      dragLogger.debug(`[拖拽] 检测到需要扩展时长: ${config.waveformStore.totalDuration}ms -> ${validation.suggestedTotalDuration}ms`);
      config.waveformStore.totalDuration = validation.suggestedTotalDuration;
    }

    // 应用验证结果，并添加安全检查确保与最终更新逻辑一致
    newStartTime = Math.max(0, validation.adjustedValue || newStartTime);
    updateEventDragValues(newStartTime - dragStartEventTime.value);
  };

  // 初始化事件处理器
  const eventHandlers = useDragEventHandlers(
    config,
    autoScrollConfig,
    isDragging,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartX,
    dragStartY,
    longPressTimer,
    startDragging,
    resetDragStateBase,
    handleEventDrag,
    handleTransientPeakDrag,
    handleCurvePointDrag,
    updateStoreInRealTime,
    handleAutoScroll,
    executeDragUpdate,
    isFrequencyAdjustmentKeyPressed,
    // 临时全局强度管理函数
    disableTemporaryGlobalIntensity,
    shouldUpdateRealGlobalIntensity,
    calculateNewGlobalIntensity,
    currentDraggedIntensity
  );
  const { handleMouseMove, handleMouseUp, setupLongPressTimer, cleanup } = eventHandlers;

  // 扩展重置拖拽状态功能
  const resetDragState = () => {
    resetDragStateBase();
    cleanup();

    // 重置光标样式
    if (config.canvas.value) {
      config.canvas.value.style.cursor = "default";
    }

    // 隐藏事件tooltip
    if (config.hideEventTooltip) {
      config.hideEventTooltip();
    }
  };

  // 处理鼠标按下事件
  const handleMouseDown = (mouseEvent: MouseEvent) => {
    mouseEvent.preventDefault();
    const canvas = config.canvas.value;
    const events = getEvents();
    if (!canvas || !events || events.length === 0) return;

    // 清除之前的长按计时器
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }

    // 获取点击坐标
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    const clickX = (mouseEvent.clientX - rect.left) * dpr;
    const clickY = (mouseEvent.clientY - rect.top) * dpr;

    // 初始化拖拽状态
    initializeDragState(clickX, clickY);

    // 使用 HitTestService 统一命中检测
    const hitResult = hitTestEvents(clickX, clickY, events, config.mapTimeToXLocal, config.mapIntensityToYLocal, {
      pointRadius: SELECTED_POINT_RADIUS * 2,
      curvePointRadius: SELECTED_POINT_RADIUS * 2,
    });

    if (hitResult.event) {
      const event = hitResult.event;

      if (event.type === "transient") {
        // 使用瞬态事件处理器
        const result = handleTransientMouseDown(clickX, clickY, event as RenderableTransientEvent);
        if (result.handled && result.targetType) {
          setupLongPressTimer(event, result.targetType);
        }
      } else if (event.type === "continuous") {
        // 使用连续事件处理器
        const result = handleContinuousMouseDown(clickX, clickY, event as RenderableContinuousEvent, hitResult.curvePointIndex);
        if (result.handled && result.targetType) {
          setupLongPressTimer(event, result.targetType, result.curveIndex);
        }
      }
    }
  };

  return {
    // 拖拽状态（从 useDragState 导出）
    isDragging,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartX,
    dragStartY,
    dragStartEventTime,
    longPressTimer,
    currentDraggedIntensity,
    currentDraggedTimeOffset,
    currentDraggedRawIntensity,
    currentDraggedFrequency,
    currentDraggedRelativeFrequency,

    // 临时全局强度状态
    isUsingTemporaryGlobalIntensity,
    originalGlobalIntensity,

    // 拖拽方法
    resetDragState,
    createPreviewEvent,

    // 事件处理器
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
  };
}
