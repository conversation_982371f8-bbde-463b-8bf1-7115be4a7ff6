/**
 * 波形坐标转换服务
 * 统一管理所有坐标转换逻辑，支持虚拟滚动
 */

import {
  INTENSITY_RANGE,
  COORDINATE_PRECISION
} from "../config/waveform-constants";
import { getGlobalEffectiveGlobalIntensity } from "../composables/useDragState";

export interface CoordinateConfig {
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  safeOffset: number;
}

export interface GraphAreaInfo {
  width: number;
  height: number;
  logicalWidth: number;
}

export class WaveformCoordinateService {
  private config: CoordinateConfig;
  private graphArea: GraphAreaInfo = {
    width: 0,
    height: 0,
    logicalWidth: 0,
  };
  private canvasHeight: number = 0;
  private effectiveDuration: number = 0;
  private virtualOffset: number = 0;

  constructor(config: CoordinateConfig) {
    this.config = config;
  }

  /**
   * 更新图形区域信息
   */
  updateGraphArea(
    canvasWidth: number,
    canvasHeight: number,
    logicalCanvasWidth: number,
    effectiveDuration: number,
    virtualOffset: number = 0
  ): void {
    this.graphArea = {
      width: canvasWidth - this.config.padding.left - this.config.padding.right - this.config.safeOffset,
      height: canvasHeight - this.config.padding.top - this.config.padding.bottom,
      logicalWidth: logicalCanvasWidth - this.config.padding.left - this.config.padding.right - this.config.safeOffset,
    };
    this.canvasHeight = canvasHeight;
    this.effectiveDuration = effectiveDuration;
    this.virtualOffset = virtualOffset;
  }

  /**
   * 时间映射到X坐标（支持虚拟滚动）
   */
  mapTimeToX(time: number): number {
    // 改进边界检查：同时检查有效时长和时间值
    if (this.effectiveDuration <= 0 || time < 0) {
      return this.config.padding.left + this.config.safeOffset;
    }

    // 计算逻辑X坐标（基于逻辑宽度）
    const logicalX = (time / this.effectiveDuration) * this.graphArea.logicalWidth;

    // 应用虚拟偏移量，转换为物理坐标
    const physicalX = logicalX - this.virtualOffset + this.config.safeOffset;

    return physicalX;
  }

  /**
   * 时间映射到逻辑X坐标（不考虑虚拟滚动偏移）
   */
  mapTimeToLogicalX(time: number): number {
    // 改进边界检查：同时检查有效时长和时间值
    if (this.effectiveDuration <= 0 || time < 0) {
      return this.config.padding.left + this.config.safeOffset;
    }

    const x = (time / this.effectiveDuration) * this.graphArea.logicalWidth;
    return this.config.safeOffset + Math.max(0, Math.min(x, this.graphArea.logicalWidth));
  }

  /**
   * 强度映射到Y坐标
   * @param intensity 强度值
   * @param maxIntensity 最大强度值（默认使用常量定义的最大值）
   */
  mapIntensityToY(intensity: number, maxIntensity: number = INTENSITY_RANGE.MAX): number {
    // 如果没有指定maxIntensity，尝试使用动态全局强度
    if (maxIntensity === INTENSITY_RANGE.MAX) {
      const globalIntensity = getGlobalEffectiveGlobalIntensity();
      if (globalIntensity !== undefined) {
        maxIntensity = globalIntensity;
      }
    }

    // 使用传入的最大强度值或常量定义的强度范围
    const normalizedIntensity = Math.max(INTENSITY_RANGE.MIN, Math.min(maxIntensity, intensity));
    const y = this.config.padding.top + this.graphArea.height - (normalizedIntensity / maxIntensity) * this.graphArea.height;
    return Math.max(this.config.padding.top, Math.min(y, this.canvasHeight - this.config.padding.bottom));
  }

  /**
   * Y坐标映射到强度
   * @param y Y坐标
   * @param maxIntensity 最大强度值（默认使用常量定义的最大值）
   */
  mapYToIntensity(y: number, maxIntensity: number = INTENSITY_RANGE.MAX): number {
    // 如果没有指定maxIntensity，尝试使用动态全局强度
    if (maxIntensity === INTENSITY_RANGE.MAX) {
      const globalIntensity = getGlobalEffectiveGlobalIntensity();
      if (globalIntensity !== undefined) {
        maxIntensity = globalIntensity;
      }
    }

    const intensity = maxIntensity - ((y - this.config.padding.top) / this.graphArea.height) * maxIntensity;
    return Math.floor(Math.max(INTENSITY_RANGE.MIN, Math.min(maxIntensity, intensity)));
  }

  /**
   * X坐标差值映射到时间偏移（支持虚拟滚动）
   */
  mapXToTimeOffset(pixelOffset: number): number {
    if (this.effectiveDuration <= 0 || this.graphArea.logicalWidth <= 0) return 0;
    return (pixelOffset / this.graphArea.logicalWidth) * this.effectiveDuration;
  }

  /**
   * X偏移到时间偏移（支持虚拟滚动）
   */
  mapXOffsetToTimeOffset(pixelOffset: number): number {
    if (this.effectiveDuration <= 0 || this.graphArea.logicalWidth <= 0) return 0;
    return (pixelOffset / this.graphArea.logicalWidth) * this.effectiveDuration;
  }

  /**
   * X坐标转换为时间点（支持虚拟滚动）
   */
  convertXToTime(x: number): number {
    if (this.effectiveDuration <= 0 || this.graphArea.logicalWidth <= 0) return 0;

    // 将物理坐标转换为逻辑坐标
    const logicalX = x + this.virtualOffset;
    const relativeX = Math.max(0, Math.min(logicalX, this.graphArea.logicalWidth));
    const time = (relativeX / this.graphArea.logicalWidth) * this.effectiveDuration;

    // 根据配置决定是否取整，避免精度丢失
    return Math.max(0, COORDINATE_PRECISION.TIME_COORDINATE_ROUND ? Math.round(time) : time);
  }

  /**
   * 检查点是否在半径范围内
   */
  isPointInRadius(x1: number, y1: number, x2: number, y2: number, radius: number): boolean {
    const dx = x1 - x2;
    const dy = y1 - y2;
    return dx * dx + dy * dy <= radius * radius;
  }

  /**
   * 计算原始强度比例
   */
  calculateRawIntensity(drawIntensity: number, globalIntensity: number): number {
    if (globalIntensity <= 0) return 0;
    const raw = drawIntensity / globalIntensity;
    return Math.max(0, Math.min(1, raw));
  }

  /**
   * 获取图形区域信息
   */
  getGraphArea(): GraphAreaInfo {
    return { ...this.graphArea };
  }

  /**
   * 获取当前有效时长
   */
  getEffectiveDuration(): number {
    return this.effectiveDuration;
  }

  /**
   * 获取当前虚拟偏移量
   */
  getVirtualOffset(): number {
    return this.virtualOffset;
  }

  /**
   * 更新虚拟偏移量
   */
  updateVirtualOffset(offset: number): void {
    this.virtualOffset = offset;
  }

  /**
   * 获取画布高度
   */
  getCanvasHeight(): number {
    return this.canvasHeight;
  }

  /**
   * 获取配置信息
   */
  getConfig(): CoordinateConfig {
    return { ...this.config };
  }
}
