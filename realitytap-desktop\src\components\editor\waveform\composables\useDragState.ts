import type { RenderableContinuousEvent, RenderableEvent, RenderableTransientEvent } from "@/types/haptic-editor";
import { ref, readonly, type Ref } from "vue";
import { calculateRawIntensity } from "../utils/coordinate";

// 拖拽状态类型定义
export type DragTarget = "event" | "transientPeak" | "continuousCurvePoint" | null;

// 拖拽状态接口
export interface DragState {
  isDragging: Ref<boolean>;
  draggedEvent: Ref<RenderableEvent | null>;
  draggingTarget: Ref<DragTarget>;
  draggedCurveIndex: Ref<number>;
  dragStartX: Ref<number>;
  dragStartY: Ref<number>;
  dragStartEventTime: Ref<number>;
  longPressTimer: Ref<any>;
  currentDraggedIntensity: Ref<number>;
  currentDraggedTimeOffset: Ref<number>;
  currentDraggedRawIntensity: Ref<number>;
  currentDraggedFrequency: Ref<number>;
  currentDraggedRelativeFrequency: Ref<number>;
  // 临时全局强度相关状态
  originalGlobalIntensity: Ref<number>;
  temporaryGlobalIntensity: Ref<number>;
  isUsingTemporaryGlobalIntensity: Ref<boolean>;
}

// 拖拽状态管理 composable
export function useDragState() {
  // 基础拖拽状态
  const isDragging = ref<boolean>(false);
  const draggedEvent = ref<RenderableEvent | null>(null);
  const draggingTarget = ref<DragTarget>(null);
  const draggedCurveIndex = ref<number>(-1);
  const dragStartX = ref<number>(0);
  const dragStartY = ref<number>(0);
  const dragStartEventTime = ref<number>(0);
  const longPressTimer = ref<any>(null);

  // 拖拽过程中的临时值
  const currentDraggedIntensity = ref<number>(0);
  const currentDraggedTimeOffset = ref<number>(0);
  const currentDraggedRawIntensity = ref<number>(0);
  const currentDraggedFrequency = ref<number>(0);
  const currentDraggedRelativeFrequency = ref<number>(0);

  // 临时全局强度状态
  const originalGlobalIntensity = ref<number>(0);
  const temporaryGlobalIntensity = ref<number>(100);
  const isUsingTemporaryGlobalIntensity = ref<boolean>(false);

  // 重置拖拽状态
  const resetDragState = () => {
    isDragging.value = false;
    draggedEvent.value = null;
    draggingTarget.value = null;
    draggedCurveIndex.value = -1;
    dragStartX.value = 0;
    dragStartY.value = 0;
    dragStartEventTime.value = 0;
    currentDraggedIntensity.value = 0;
    currentDraggedTimeOffset.value = 0;
    currentDraggedRawIntensity.value = 0;
    currentDraggedFrequency.value = 0;
    currentDraggedRelativeFrequency.value = 0;

    // 重置临时全局强度状态
    originalGlobalIntensity.value = 0;
    temporaryGlobalIntensity.value = 100;
    isUsingTemporaryGlobalIntensity.value = false;

    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
  };

  // 初始化拖拽状态
  const initializeDragState = (clickX: number, clickY: number) => {
    draggingTarget.value = null;
    draggedEvent.value = null;
    draggedCurveIndex.value = -1;
    dragStartX.value = clickX;
    dragStartY.value = clickY;
    currentDraggedIntensity.value = 0;
    currentDraggedTimeOffset.value = 0;
    currentDraggedRawIntensity.value = 0;
    currentDraggedFrequency.value = 0;
    currentDraggedRelativeFrequency.value = 0;
  };

  // 设置瞬态峰值点拖拽状态
  const setupTransientPeakDrag = (event: RenderableEvent, transient: RenderableTransientEvent) => {
    draggedEvent.value = event;
    dragStartEventTime.value = event.startTime;
    draggingTarget.value = "transientPeak";
    currentDraggedIntensity.value = transient.intensity;
  };

  // 设置整个事件拖拽状态
  const setupEventDrag = (event: RenderableEvent) => {
    draggedEvent.value = event;
    dragStartEventTime.value = event.startTime;
    draggingTarget.value = "event";
  };

  // 设置曲线点拖拽状态
  const setupCurvePointDrag = (event: RenderableEvent, continuousEvent: RenderableContinuousEvent, curveIndex: number) => {
    const curvePoint = continuousEvent.curves[curveIndex];

    draggedEvent.value = event;
    dragStartEventTime.value = event.startTime;
    draggedCurveIndex.value = curveIndex;
    draggingTarget.value = "continuousCurvePoint";
    currentDraggedIntensity.value = curvePoint.drawIntensity;
    currentDraggedRawIntensity.value = calculateRawIntensity(curvePoint.drawIntensity, continuousEvent.eventIntensity);
    currentDraggedTimeOffset.value = curvePoint.timeOffset;
    currentDraggedRelativeFrequency.value = curvePoint.relativeCurveFrequency;
    currentDraggedFrequency.value = curvePoint.curveFrequency;
  };

  // 开始拖拽操作
  const startDragging = () => {
    isDragging.value = true;
  };

  // 更新拖拽值 - 整个事件
  const updateEventDragValues = (timeOffset: number) => {
    currentDraggedTimeOffset.value = timeOffset;
  };

  // 更新拖拽值 - 瞬态峰值点
  const updateTransientPeakDragValues = (intensity: number) => {
    currentDraggedIntensity.value = intensity;
  };

  // 更新拖拽值 - 连续事件曲线点（频率模式）
  const updateCurvePointFrequencyDragValues = (relativeFrequency: number, absoluteFrequency: number) => {
    currentDraggedRelativeFrequency.value = relativeFrequency;
    currentDraggedFrequency.value = absoluteFrequency;
  };

  // 更新拖拽值 - 连续事件曲线点（位置/强度模式）
  const updateCurvePointPositionDragValues = (timeOffset: number, intensity: number, rawIntensity: number) => {
    currentDraggedTimeOffset.value = timeOffset;
    currentDraggedIntensity.value = intensity;
    currentDraggedRawIntensity.value = rawIntensity;
  };

  // 临时全局强度管理函数

  /**
   * 启用临时全局强度模式
   * @param currentGlobalIntensity 当前真实的全局强度
   */
  const enableTemporaryGlobalIntensity = (currentGlobalIntensity: number) => {
    if (!isUsingTemporaryGlobalIntensity.value) {
      originalGlobalIntensity.value = currentGlobalIntensity;
      temporaryGlobalIntensity.value = 100;
      isUsingTemporaryGlobalIntensity.value = true;
    }
  };

  /**
   * 禁用临时全局强度模式
   */
  const disableTemporaryGlobalIntensity = () => {
    isUsingTemporaryGlobalIntensity.value = false;
    originalGlobalIntensity.value = 0;
    temporaryGlobalIntensity.value = 100;
  };

  /**
   * 获取当前应该使用的全局强度
   * @returns 如果在临时模式下返回100，否则返回原始全局强度
   */
  const getCurrentEffectiveGlobalIntensity = (): number => {
    return isUsingTemporaryGlobalIntensity.value ? temporaryGlobalIntensity.value : originalGlobalIntensity.value;
  };

  /**
   * 检查是否需要更新真实的全局强度
   * @param targetIntensity 目标强度值
   * @returns 是否需要更新全局强度
   */
  const shouldUpdateRealGlobalIntensity = (targetIntensity: number): boolean => {
    return isUsingTemporaryGlobalIntensity.value && targetIntensity > originalGlobalIntensity.value;
  };

  /**
   * 计算新的全局强度值
   * @param targetIntensity 目标强度值
   * @returns 新的全局强度值
   */
  const calculateNewGlobalIntensity = (targetIntensity: number): number => {
    if (!isUsingTemporaryGlobalIntensity.value) {
      return originalGlobalIntensity.value;
    }
    return Math.max(originalGlobalIntensity.value, Math.ceil(targetIntensity));
  };

  // 检查是否正在拖拽
  const isDraggingActive = () => isDragging.value;

  // 检查是否有拖拽目标
  const hasDragTarget = () => draggedEvent.value !== null && draggingTarget.value !== null;

  // 获取当前拖拽的事件类型
  const getDraggedEventType = () => draggedEvent.value?.type || null;

  // 获取当前拖拽目标类型
  const getDragTargetType = () => draggingTarget.value;

  // 检查是否在拖拽特定类型的目标
  const isDraggingEventType = (targetType: DragTarget) => draggingTarget.value === targetType;

  // 检查是否在拖拽特定事件
  const isDraggingEvent = (eventId: string) => draggedEvent.value?.id === eventId;

  // 获取拖拽状态快照（用于调试）
  const getDragStateSnapshot = () => ({
    isDragging: isDragging.value,
    draggedEventId: draggedEvent.value?.id || null,
    draggingTarget: draggingTarget.value,
    draggedCurveIndex: draggedCurveIndex.value,
    currentValues: {
      intensity: currentDraggedIntensity.value,
      timeOffset: currentDraggedTimeOffset.value,
      rawIntensity: currentDraggedRawIntensity.value,
      frequency: currentDraggedFrequency.value,
      relativeFrequency: currentDraggedRelativeFrequency.value,
    },
  });

  // 拖拽状态对象
  const dragState: DragState = {
    isDragging,
    draggedEvent,
    draggingTarget,
    draggedCurveIndex,
    dragStartX,
    dragStartY,
    dragStartEventTime,
    longPressTimer,
    currentDraggedIntensity,
    currentDraggedTimeOffset,
    currentDraggedRawIntensity,
    currentDraggedFrequency,
    currentDraggedRelativeFrequency,
    // 临时全局强度状态
    originalGlobalIntensity,
    temporaryGlobalIntensity,
    isUsingTemporaryGlobalIntensity,
  };

  return {
    // 状态对象
    ...dragState,

    // 状态管理方法
    resetDragState,
    initializeDragState,
    setupTransientPeakDrag,
    setupEventDrag,
    setupCurvePointDrag,
    startDragging,

    // 值更新方法
    updateEventDragValues,
    updateTransientPeakDragValues,
    updateCurvePointFrequencyDragValues,
    updateCurvePointPositionDragValues,

    // 临时全局强度管理方法
    enableTemporaryGlobalIntensity,
    disableTemporaryGlobalIntensity,
    getCurrentEffectiveGlobalIntensity,
    shouldUpdateRealGlobalIntensity,
    calculateNewGlobalIntensity,

    // 状态查询方法
    isDraggingActive,
    hasDragTarget,
    getDraggedEventType,
    getDragTargetType,
    isDraggingEventType,
    isDraggingEvent,
    getDragStateSnapshot,

    // 临时全局强度状态查询
    isUsingTemporaryGlobalIntensity: readonly(isUsingTemporaryGlobalIntensity),
    originalGlobalIntensity: readonly(originalGlobalIntensity),
  };
}

// 导出全局访问函数，供坐标转换系统使用
let globalGetCurrentEffectiveGlobalIntensity: (() => number) | null = null;

export function setGlobalEffectiveGlobalIntensityGetter(getter: () => number) {
  globalGetCurrentEffectiveGlobalIntensity = getter;
}

export function getGlobalEffectiveGlobalIntensity(): number | undefined {
  return globalGetCurrentEffectiveGlobalIntensity ? globalGetCurrentEffectiveGlobalIntensity() : undefined;
}
